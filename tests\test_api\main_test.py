import requests
import json
import re
import os
import time

# API Configuration
API_URL = "http://localhost:26031/chatbot"
USER_ID = "test_user_001"
OUTPUT_FILE = "../api_test_results.json"
# TEST_CASE_FILE = "tests/test_api/test_case.json"

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
TEST_CASE_FILENAME = 'test_case.json'
TEST_CASE_FILE = os.path.join(SCRIPT_DIR, '..', 'test_api', TEST_CASE_FILENAME)
TEST_CASE_FILE = os.path.normpath(TEST_CASE_FILE)

# --- Load Test Cases from JSON file ---
try:
    with open(TEST_CASE_FILE, 'r', encoding='utf-8') as f:
        test_cases = json.load(f)
except FileNotFoundError:
    print(f"Lỗi: Không tìm thấy file test case tại {TEST_CASE_FILE}")
    test_cases = []
except json.JSONDecodeError:
    print(f"Lỗi: Không thể giải mã file JSON từ {TEST_CASE_FILE}")
    test_cases = []
# -------------------------------------------------

results_log = []


def get_intent_suffix(intent_name):
    """Trích xuất hậu tố số từ tên intent như 'intent_1'."""
    if intent_name and isinstance(intent_name, str):
        match = re.search(r'_(\d+)$', intent_name)
        if match:
            return int(match.group(1))
    return None


print(f"Đang bắt đầu kiểm tra API cho {len(test_cases)} trường hợp...")

for i, case in enumerate(test_cases):
    expected_stt = case["stt"]
    question_text = case["question"]

    payload = {
        "text": question_text,
        "user_ID": USER_ID,
        "attachments": 0
    }

    log_entry = {
        "question_number": i + 1,
        "question_text": question_text,
        "expected_stt": expected_stt,
        "api_payload": payload,
        "api_response_raw": None,
        "detected_intent_name": None,
        "detected_intent_suffix": None,
        "confidence": None,
        "intent_ranking_top_3": [],
        "actions_response": None,
        "status": "FAIL",
        "error_message": None
    }

    try:
        print(f"Đang kiểm tra trường hợp {i + 1}/{len(test_cases)}: \"{question_text[:50]}...\"")
        response = requests.post(API_URL, json=payload, timeout=10)
        log_entry["api_response_raw"] = response.text

        response.raise_for_status()

        response_data = response.json()
        log_entry["api_response_json_parsed"] = response_data

        intent_data = response_data.get("intent")
        if intent_data:
            log_entry["detected_intent_name"] = intent_data.get("name")
            log_entry["confidence"] = intent_data.get("confidence")
            actual_intent_suffix = get_intent_suffix(log_entry["detected_intent_name"])
            log_entry["detected_intent_suffix"] = actual_intent_suffix

            if actual_intent_suffix is not None and actual_intent_suffix == expected_stt:
                log_entry["status"] = "PASS"
            else:
                log_entry["error_message"] = f"Sai lệch: Kỳ vọng Stt {expected_stt}, Nhận được Hậu tố {actual_intent_suffix}"
        else:
            log_entry["error_message"] = "Không có đối tượng 'intent' trong phản hồi API."

        # Ghi lại top 3 intent có khả năng cao nhất để có thêm thông tin
        intent_ranking = response_data.get("intent_ranking", [])
        log_entry["intent_ranking_top_3"] = intent_ranking[:3]
        log_entry["actions_response"] = response_data.get("actions")


    except requests.exceptions.HTTPError as http_err:
        log_entry["error_message"] = f"Lỗi HTTP: {http_err} (Trạng thái: {response.status_code if 'response' in locals() else 'N/A'})"
        print(f"  Lỗi: {log_entry['error_message']}")
    except requests.exceptions.ConnectionError as conn_err:
        log_entry["error_message"] = f"Lỗi kết nối: {conn_err}"
        print(f"  Lỗi: {log_entry['error_message']}")
    except requests.exceptions.Timeout as timeout_err:
        log_entry["error_message"] = f"Lỗi timeout: {timeout_err}"
        print(f"  Lỗi: {log_entry['error_message']}")
    except requests.exceptions.RequestException as req_err:
        log_entry["error_message"] = f"Đã xảy ra lỗi yêu cầu không rõ ràng: {req_err}"
        print(f"  Lỗi: {log_entry['error_message']}")
    except json.JSONDecodeError:
        log_entry["error_message"] = "Không thể giải mã phản hồi JSON từ API."
        print(f"  Lỗi: {log_entry['error_message']}")
    except Exception as e:
        log_entry["error_message"] = f"Đã xảy ra lỗi không mong muốn: {str(e)}"
        print(f"  Lỗi: {log_entry['error_message']}")

    results_log.append(log_entry)
    print(
        f"  Kết quả: {log_entry['status']}. Kỳ vọng: {expected_stt}, Nhận được: {log_entry.get('detected_intent_suffix', 'Không có')}")
    time.sleep(2)

try:
    with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
        json.dump(results_log, f, ensure_ascii=False, indent=2)
    print(f"\nKết quả kiểm tra đã được lưu vào {OUTPUT_FILE}")
except IOError:
    print(f"\nLỗi: Không thể ghi kết quả vào {OUTPUT_FILE}")

pass_count = sum(1 for r in results_log if r["status"] == "PASS")
fail_count = len(results_log) - pass_count
print(f"\n--- Tóm tắt kiểm tra ---")
print(f"Tổng số trường hợp kiểm tra: {len(results_log)}")
print(f"Đã qua: {pass_count}")
print(f"Thất bại: {fail_count}")
print("--------------------")