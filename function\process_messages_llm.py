import os
import json
import asyncio
import logging
import time
from copy import deepcopy
from typing import Dict, Any, Optional, List

from .classify.question_classifier import ClassificationQuestion, classify_response

# --- Inputs (Global configurations and paths) ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

DEFAULT_FALLBACK_MESSAGE = "Xin lỗi, tôi chưa hiểu rõ ý của bạn. Bạn có thể nói rõ hơn được không?"

# --- Logger Setup ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

DEFAULT_CONFIG: Dict[str, Any] = {
    "classifier_app_code": "TCB",
    "classifier_function_code": "tcb_classify",
    "classifier_model_llm": "llm-sea3.1",
    "classifier_url_llm_api": "http://**********:2033/api/llama3",
    "classifier_is_get_prompt_online": False,
    "debug_write_record_json": True,
    "record_json_path": os.path.join(SCRIPT_DIR, "debug_record.json")
}

def convert_buttons_from_payload(buttons_payload: List[Dict[str, Any]], platform: str) -> List[Dict[str, Any]]:
    logger.info(f"Placeholder: convert_buttons_from_payload called with platform: {platform}, buttons_payload: {buttons_payload}")
    return buttons_payload

async def rewrite_question(question: str) -> str:
    return question

class LLMChatProcessor:
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = deepcopy(DEFAULT_CONFIG)
        if config:
            self.config.update(config)
        
        try:
            self.question_classifier = ClassificationQuestion(
                app_code=self.config.get("classifier_app_code", "TCB"),
                function_code=self.config.get("classifier_function_code", "tcb_classify"),
                model_llm=self.config.get("classifier_model_llm", "llm-sea3.1"),
                url_llm_api=self.config.get("classifier_url_llm_api", "http://**********:2033/api/llama3"),
                is_get_prompt_online=self.config.get("classifier_is_get_prompt_online", False)
            )
            logger.info("ClassificationQuestion engine initialized successfully for LLMChatProcessor.")
        except Exception as e:
            logger.critical(f"Failed to initialize ClassificationQuestion engine: {e}", exc_info=True)
            raise RuntimeError("Could not initialize Question Classifier") from e


        self.user_message_history: Dict[str, set] = {}
        self.user_last_message_time: Dict[str, float] = {}
        self.message_cooldown = 5.0

    def _clean_text(self, text: str) -> str:
        return text.strip()

    async def process_message(self, record: Dict[str, Any], is_web_request: bool = False) -> Dict[str, Any]:
        message_text: str = self._clean_text(record.get("text", ""))
        
        if not message_text:
            logger.info("Empty message_text after cleaning.")
            return {"status": 0, "message": "empty_message", "action": {}}

        user_id: str = record.get("user_ID", record.get("conversation_id", "default_user"))
        current_time = time.time()

        # --- Duplicate Message Check ---
        normalized_text = message_text.lower()
        if user_id not in self.user_message_history:
            self.user_message_history[user_id] = set()
            self.user_last_message_time[user_id] = 0.0

        if normalized_text in self.user_message_history[user_id]:
            time_since_last_message = current_time - self.user_last_message_time.get(user_id, 0)
            if time_since_last_message < self.message_cooldown:
                logger.info(f"Message cooldown active for user '{user_id}': '{message_text}'")
                cooldown_response = deepcopy(record)
                cooldown_response["actions"] = ""
                cooldown_response["buttons"] = []
                cooldown_response["message"] = "cooldown"
                cooldown_response["error_code"] = 2
                cooldown_response["type"] = "text"
                if not is_web_request:
                    return {"status": 0, "message": "cooldown", "action": cooldown_response}
                return cooldown_response
        self.user_last_message_time[user_id] = current_time
        self.user_message_history[user_id].add(normalized_text)

        logger.info(f"Processing message for user '{user_id}': '{message_text}'")

        if self.config.get("debug_write_record_json", False):
            try:
                with open(self.config["record_json_path"], 'w', encoding='utf-8') as f_json:
                    json.dump(record, f_json, ensure_ascii=False, indent=2)
            except Exception as e_json:
                logger.error(f"Error writing debug record.json: {e_json}")
        
        try:
            message_text_rewritten = await rewrite_question(question=message_text)
        except Exception as e:
            message_text_rewritten = message_text

        final_response = deepcopy(record)
        user_name = record.get("user_name", "[user]")

        if is_web_request and (record.get("attachments") == 1 or (isinstance(record.get("attachments"), list) and len(record.get("attachments", [])) > 0)):
            logger.info(f"Message for user '{user_id}' has attachments.")
            final_response["actions"] = "Cảm ơn bạn đã cung cấp hình ảnh/tài liệu. Chuyên viên sẽ sớm liên hệ."
            final_response["buttons"] = []
            final_response["message"] = "success_attachment_fallback"
            final_response["error_code"] = 0
            final_response["type"] = "text"
            return final_response

        # --- Phân loại câu hỏi bằng LLM ---
        nlu_result: Dict[str, Any]
        try:
            nlu_result = await asyncio.to_thread(
                self.question_classifier.classify_question_with_llm, 
                message_text_rewritten
            )
            logger.debug(f"NLU classification result: {nlu_result}")
        except Exception as e:
            logger.error(f"Error during NLU classification: {e}", exc_info=True)
            nlu_result = self.question_classifier._create_fallback_response(message_text_rewritten, time.time())

        intent_name = nlu_result.get("intent", {}).get("name", "nlu_fallback")

        response_objects: List[Dict[str, Any]] = classify_response(intent_name)
        
        actions_text_parts = []
        buttons_payload_parts = []

        if not response_objects:
            logger.warning(f"classify_response returned empty for intent '{intent_name}'. Using default fallback.")
            actions_text_parts.append(DEFAULT_FALLBACK_MESSAGE)
        else:
            for res_obj in response_objects:
                if "text" in res_obj:
                    actions_text_parts.append(res_obj["text"])
                if "buttons" in res_obj:
                    buttons_payload_parts.extend(res_obj["buttons"])
        
        final_actions_text = "\n".join(filter(None, actions_text_parts)).strip()
        if not final_actions_text:
             final_actions_text = DEFAULT_FALLBACK_MESSAGE
             logger.warning(f"No text found in response_objects for intent '{intent_name}', using default fallback.")


        final_response["actions"] = final_actions_text.replace("[user_name]", user_name)
        final_response["buttons"] = convert_buttons_from_payload(buttons_payload_parts, platform=record.get("platform", ""))
        final_response["message"] = "success"
        final_response["error_code"] = 0
        final_response["type"] = "buttons" if final_response["buttons"] else "text"
        
        final_response['intent'] = nlu_result.get('intent')
        final_response['entities'] = nlu_result.get('entities')
        final_response['intent_ranking'] = nlu_result.get('intent_ranking')
        final_response['nlu_metadata'] = nlu_result.get('metadata')


        if not is_web_request:
            return {"status": 1, "message": "success", "action": final_response}

        logger.debug(f"Final response for user '{user_id}': {json.dumps(final_response, ensure_ascii=False, indent=2)}")
        return final_response

async def main_test_runner(config_override: Optional[Dict[str, Any]] = None):
    logger.info("Starting LLMChatProcessor test run with integrated Question Classifier...")
    try:
        processor = LLMChatProcessor(config=config_override)

        test_cases = [
            {
                "text": "Tôi muốn mở tài khoản ngân hàng.",
                "user_ID": "test_user_open_acc",
                "platform": "web",
                "user_name": "Khách Mở TK"
            },
            {
                "text": "Chào bạn",
                "user_ID": "test_user_greeting",
                "platform": "zalo",
                "user_name": "Người Chào Hỏi"
            },
            {
                "text": "lãi suất tiền gửi là bao nhiêu",
                "user_ID": "test_user_interest",
                "platform": "web",
                "user_name": "Khách Hỏi Lãi"
            },
            {
                "text": "sdjflsajdf lkasjdf lksajdf",
                "user_ID": "test_user_gibberish",
                "platform": "web",
                "user_name": "Người Nói Nhảm"
            }
        ]

        for i, test_case_data in enumerate(test_cases):
            logger.info(f"\n--- Test Case {i+1}: {test_case_data['text']} ---")
            is_web = test_case_data.get("platform") == "web"
            reply_result = await processor.process_message(test_case_data, is_web_request=is_web)
            logger.info(f"Reply Result: {json.dumps(reply_result, ensure_ascii=False, indent=2)}")
            if i == 0:
                logger.info(f"--- Test Cooldown for: {test_case_data['text']} ---")
                cooldown_reply = await processor.process_message(test_case_data, is_web_request=is_web)
                logger.info(f"Cooldown Reply Result: {json.dumps(cooldown_reply, ensure_ascii=False, indent=2)}")


    except Exception as e:
        logger.error(f"Test runner failed: {e}", exc_info=True)
    logger.info("LLMChatProcessor test run finished.")

if __name__ == '__main__':
    asyncio.run(main_test_runner())