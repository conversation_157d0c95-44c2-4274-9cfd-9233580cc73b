[2025-06-25 17:30:17,426]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:30:17,426]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:30:17,428]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.4289153: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: ph\u1ea3i l\u00e0m g\u00ec \u0111\u1ec3 kh\u1eafc ph\u1ee5c \u1ea1?\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:32:31,837]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:32:31,838]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:32:31,838]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.8386707: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: ph\u1ea3i l\u00e0m g\u00ec \u0111\u1ec3 kh\u1eafc ph\u1ee5c \u1ea1?\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:32:32,578]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.8386707: ["1"]; time analysis [real - llm]: 0.7399289608001709 - 0.1333305835723877
[2025-06-25 17:32:32,581]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.8386707: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:33:15,375]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:33:15,376]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:33:15,377]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.3777394: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: H\u01b0\u1edbng d\u1eabn c\u00e1ch vu\u01b0\u1ee3t ng\u1ee5c\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:33:15,632]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.3777394: ["0"]; time analysis [real - llm]: 0.2550790309906006 - 0.13416266441345215
[2025-06-25 17:33:15,633]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.3777394: [{"answer": "0", "is_valid": true, "answer_norm": "0"}]
[2025-06-25 17:33:37,632]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:33:37,632]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:33:37,633]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.633851: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: C\u1ea7n t\u01b0 v\u1ea5n\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:33:41,931]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.633851: ["1"]; time analysis [real - llm]: 4.297633409500122 - 0.13476228713989258
[2025-06-25 17:33:41,931]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.633851: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:34:53,384]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:34:53,384]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:34:53,385]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.3852875: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: Mua nh\u00e0 mu\u1ed1n vay th\u00ec c\u1ea7n chu\u1ea9n b\u1ecb gi\u1ea5y t\u1edd g\u00ec?\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:34:53,857]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.3852875: ["1"]; time analysis [real - llm]: 0.4723641872406006 - 0.13324785232543945
[2025-06-25 17:34:53,858]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.3852875: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:35:06,296]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:35:06,297]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:35:06,299]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.299474: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: ad \u01a1i\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:35:06,510]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.299474: ["1"]; time analysis [real - llm]: 0.211317777633667 - 0.13821005821228027
[2025-06-25 17:35:06,511]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.299474: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:37:32,845]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:37:32,846]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:37:32,847]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.8479543: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: Em.mu\u1ed1n h\u1ecfi c\u00e1ch l\u00e0m th\u1ebb c\u1ee9ng\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:37:33,060]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.8479543: ["1"]; time analysis [real - llm]: 0.2126011848449707 - 0.13700604438781738
[2025-06-25 17:37:33,061]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.8479543: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:37:46,883]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:37:46,884]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:37:46,886]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.8867264: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: Tr\u00ean app \u1ea1\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:37:48,421]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.8867264: ["1"]; time analysis [real - llm]: 1.534538745880127 - 0.28452539443969727
[2025-06-25 17:37:48,421]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.8867264: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:37:59,246]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:37:59,247]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:37:59,249]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.2491384: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: vi em co the roi\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:38:07,138]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.2491384: ["0"]; time analysis [real - llm]: 7.889281511306763 - 0.13969850540161133
[2025-06-25 17:38:07,139]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.2491384: [{"answer": "0", "is_valid": true, "answer_norm": "0"}]
[2025-06-25 17:38:28,508]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:38:28,509]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:38:28,511]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.5102978: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: M\u00ecnh l\u00e0m \u1edf n\u01b0\u1edbc ngo\u00e0i.m\u00ecnh c\u00f3 th\u1ebb techcombank l\u00e0m \u1edf Vi\u1ec7t Nam gi\u1edd m\u00ecnh b\u00ean n\u00e0y m\u00ecnh chuy\u1ec3n ti\u1ec1n v\u00e0o th\u1ebb Vi\u1ec7t Nam c\u1ee7a m\u00ecnh th\u00ec c\u00f3 ph\u1ea3i m\u1ea5t thu\u1ebf kh\u00f4ng\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:38:28,926]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.5102978: ["1"]; time analysis [real - llm]: 0.416348934173584 - 0.13808369636535645
[2025-06-25 17:38:28,927]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.5102978: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:40:06,887]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:40:06,888]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:40:06,890]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.8900945: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: ************\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:40:07,573]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.8900945: ["0"]; time analysis [real - llm]: 0.6834065914154053 - 0.1357424259185791
[2025-06-25 17:40:07,574]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.8900945: [{"answer": "0", "is_valid": true, "answer_norm": "0"}]
[2025-06-25 17:40:19,691]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:40:19,691]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:40:19,693]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.6938443: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: vi em muon chuyen tien cho cha em ...ma em khong biet stk\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:40:19,932]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.6938443: ["1"]; time analysis [real - llm]: 0.2385849952697754 - 0.13741230964660645
[2025-06-25 17:40:19,932]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.6938443: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:40:33,606]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:40:33,607]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:40:33,608]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.607316: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: D\u1ea1a\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:40:36,003]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.607316: ["0"]; time analysis [real - llm]: 2.3963510990142822 - 0.34039998054504395
[2025-06-25 17:40:36,004]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.607316: [{"answer": "0", "is_valid": true, "answer_norm": "0"}]
[2025-06-25 17:41:00,553]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:41:00,553]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:41:00,556]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.556528: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: l\u00e0m s \u0111\u1ec3\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:41:00,804]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.556528: ["0"]; time analysis [real - llm]: 0.24766087532043457 - 0.13042783737182617
[2025-06-25 17:41:00,805]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.556528: [{"answer": "0", "is_valid": true, "answer_norm": "0"}]
[2025-06-25 17:41:15,498]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:41:15,499]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:41:15,500]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.5006466: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: Em c\u1ea7n h\u1ed7 tr\u1ee3\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:41:15,712]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.5006466: ["0"]; time analysis [real - llm]: 0.2117605209350586 - 0.10295224189758301
[2025-06-25 17:41:15,712]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.5006466: [{"answer": "0", "is_valid": true, "answer_norm": "0"}]
[2025-06-25 17:43:04,980]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:43:04,980]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:43:04,981]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.9814773: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: E c\u00f3 qu\u00e1n \u0111ang kinh doanh\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:43:05,211]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.9814773: ["0"]; time analysis [real - llm]: 0.23005437850952148 - 0.1348419189453125
[2025-06-25 17:43:05,212]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.9814773: [{"answer": "0", "is_valid": true, "answer_norm": "0"}]
[2025-06-25 17:43:21,374]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:43:21,374]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:43:21,375]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.375497: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: tk e b\u1ecbi tr\u1eeb \u0111i ph\u00ed th\u01b0\u1eddng ni\u00ean \u1ea1\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:43:21,609]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.375497: ["1"]; time analysis [real - llm]: 0.23389577865600586 - 0.13477587699890137
[2025-06-25 17:43:21,609]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.375497: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:45:32,728]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:45:32,728]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:45:32,730]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm 1750848332.7307277: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: D\u1ea1 b\u00ean n\u01b0\u1edbc ngo\u00e0i \u0111ang c\u1ea7n t\u00e0i li\u1ec7u bao g\u1ed3m stk, m\u00e3 SWIFT \u0111\u1ecba ch\u1ec9 ng nh\u1eadn v\u00e0 t\u00ean ng nh\u1eadn . M\u1ed9t b\u1ea3n sao b\u00e1o c\u00e1o ng\u00e2n h\u00e0ng c\u1ee7a t\u00f4i \u1ea1 , ng\u00e2n h\u00e0ng h\u1ed7 tr\u1ee3 t\u00f4i nh\u00e9\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:45:36,950]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm 1750848332.7307277: ["1"]; time analysis [real - llm]: 4.2202112674713135 - 0.11730480194091797
[2025-06-25 17:45:36,952]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm 1750848332.7307277: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:45:56,070]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:45:56,070]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:45:56,071]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.0719247: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: Co m\u01a1 the online dc ko an\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:45:56,388]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.0719247: ["1"]; time analysis [real - llm]: 0.31673264503479004 - 0.2267768383026123
[2025-06-25 17:45:56,389]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.0719247: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:46:10,588]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:46:10,590]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:46:10,591]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.5919244: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: T\u00e0i li\u1ec7u c\u00f3 \u0111\u00fang v\u1edbi y\u00eau c\u1ea7u c\u1ee7a n\u01b0\u1edbc ngo\u00e0i ko\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:46:19,585]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.5919244: ["1"]; time analysis [real - llm]: 8.992513656616211 - 0.13168740272521973
[2025-06-25 17:46:19,586]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.5919244: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:46:55,429]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:46:55,430]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:46:55,431]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.4314067: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: T\u00f4i mu\u1ed1n s\u1eed d\u1ee5ng d\u1ecbch v\u1ee5 my cash th\u00ec \u0111i\u1ec1u ki\u1ec7n nh\u01b0 th\u1ebf n\u00e0o\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:46:55,968]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.4314067: ["1"]; time analysis [real - llm]: 0.5374212265014648 - 0.44122767448425293
[2025-06-25 17:46:55,969]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.4314067: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:47:25,396]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:47:25,397]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:47:25,398]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.3979356: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: C\u1ee5 th\u1ec3 l\u00e0 Malaysia\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:47:25,641]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.3979356: ["0"]; time analysis [real - llm]: 0.24352645874023438 - 0.13325786590576172
[2025-06-25 17:47:25,642]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.3979356: [{"answer": "0", "is_valid": true, "answer_norm": "0"}]
[2025-06-25 17:47:44,307]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:47:44,308]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:47:44,308]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm 1750848464.3083665: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: E c\u00f3 b\u1ecf ti\u1ec1n ti\u1ebft ki\u1ec7m v\u00f4 \u0111\u00e2y, c\u00f3 c\u00e1ch n\u00e0o \u0111\u1ec3 coi ti\u1ec1n l\u00e0 l\u1ea7n sau ti\u1ebfp t\u1ee5c b\u1ecf ti\u1ec1n b\u1eb1ng c\u1ea1cha n\u00e0o?\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:47:45,643]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm 1750848464.3083665: ["1"]; time analysis [real - llm]: 1.3352932929992676 - 0.10403656959533691
[2025-06-25 17:47:45,644]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm 1750848464.3083665: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:48:24,036]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:48:24,037]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:48:24,039]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.0387423: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: T\u01b0 v\u1ea5n vi\u00ean\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:48:24,771]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.0387423: ["1"]; time analysis [real - llm]: 0.7324566841125488 - 0.09741973876953125
[2025-06-25 17:48:24,771]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.0387423: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:52:27,573]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:52:27,574]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:52:27,575]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.5742853: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: E \u0111ang c\u1ea7n s\u1eed d\u1ee5ng cho qu\u00e1n \u1ea1\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:52:27,957]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.5742853: ["1"]; time analysis [real - llm]: 0.38367795944213867 - 0.11195945739746094
[2025-06-25 17:52:27,959]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.5742853: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:52:42,401]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:52:42,402]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:52:42,404]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.4042752: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: th\u00ec th\u1ee7 t\u1ee5c nh\u01b0 th\u1ebf n\u00e0o \u0111\u1ec3 c\u00f3 th\u1ec3 ho\u00e0n ti\u1ec1n l\u1ea1i \u1ea1\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:52:42,529]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.4042752: ["1"]; time analysis [real - llm]: 0.1248936653137207 - 0.09326720237731934
[2025-06-25 17:52:42,529]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.4042752: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:53:02,225]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:53:02,226]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:53:02,227]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.2270837: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: t\u00f4i \u0111ang c\u00f3 th\u1ebb UOB PremierMiles th\u00ec c\u00f3 th\u1ec3 sang ngang th\u1ebb \u0111\u01b0\u1ee3c kh\u00f4ng?\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:53:02,479]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.2270837: ["1"]; time analysis [real - llm]: 0.2517354488372803 - 0.10275387763977051
[2025-06-25 17:53:02,480]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.2270837: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:54:01,313]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:54:01,313]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:54:01,314]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.3140578: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: t\u00f4i g\u1ecdi hotline nh\u01b0ng c\u00e1c b\u1ea1n \u1ea5y n\u00f3i ph\u1ea3i ra chi nh\u00e1nh, b\u00ean m\u00ecnh k c\u00f3 telesale g\u1ecdi l\u1ea1i ah?\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:54:01,499]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.3140578: ["1"]; time analysis [real - llm]: 0.18560385704040527 - 0.13488507270812988
[2025-06-25 17:54:01,500]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.3140578: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:55:21,681]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:55:21,682]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:55:21,682]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.6824803: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: h\u00ec n\u1ebfu l\u1ea1i \u0111\u01b0\u1ee3c ti\u1ec1n ck nh\u1ea7m,m\u00ecnh c\u00f2n ph\u1ea3i c\u1ea3m \u01a1n ng\u00e2n h\u00e0ng techcombank nhi\u1ec1u h\u01a1n \u1ea1\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:55:21,878]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.6824803: ["1"]; time analysis [real - llm]: 0.195814847946167 - 0.0996546745300293
[2025-06-25 17:55:21,879]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.6824803: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:55:34,571]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:55:34,571]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:55:34,572]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.572437: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: .\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:55:34,736]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.572437: ["0"]; time analysis [real - llm]: 0.1643078327178955 - 0.09219574928283691
[2025-06-25 17:55:34,738]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.572437: [{"answer": "0", "is_valid": true, "answer_norm": "0"}]
[2025-06-25 17:55:47,367]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:55:47,367]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:55:47,367]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.3672035: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: Em b\u1ecb b\u1ea1n l\u1ea5y th\u00f4ng tin\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:55:54,122]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.3672035: ["1"]; time analysis [real - llm]: 6.755297422409058 - 0.13482308387756348
[2025-06-25 17:55:54,123]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.3672035: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:56:09,514]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:56:09,515]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:56:09,515]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.5151818: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: \u0110\u1ec3 \u0111\u1ed5i th\u1ebb t\u1eeb sang th\u1ebb ch\u00edp m\u00ecnh c\u1ea7n \u0111\u00eam theo j ak\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:56:09,995]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.5151818: ["1"]; time analysis [real - llm]: 0.48018836975097656 - 0.23490667343139648
[2025-06-25 17:56:09,996]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.5151818: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:56:26,752]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:56:26,752]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:56:26,752]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.75278: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: mi\u00f2nh \u0111\u00e3 g\u1eedi r\u1ed3i\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:56:26,930]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.75278: ["0"]; time analysis [real - llm]: 0.17805814743041992 - 0.1353139877319336
[2025-06-25 17:56:26,931]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.75278: [{"answer": "0", "is_valid": true, "answer_norm": "0"}]
[2025-06-25 17:56:49,285]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:56:49,285]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:56:49,285]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.2854354: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: em mu\u1ed1n bi\u1ebft l\u00e0 sau khi thanh to\u00e1n sau bao l\u00e2u s\u1ebd dud\u1ee3c nh\u1eadn\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:56:49,534]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.2854354: ["1"]; time analysis [real - llm]: 0.24938678741455078 - 0.09944987297058105
[2025-06-25 17:56:49,535]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.2854354: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:57:17,649]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:57:17,650]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:57:17,650]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.6502059: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: Ng\u00e2n h\u00e0ng m\u00ecnh chi nh\u00e1nh \u1edf Ki\u1ebfn An c\u00f3 tuy\u1ec3n d\u1ee5ng kh\u00f4ng \u1ea1\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:57:17,900]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.6502059: ["1"]; time analysis [real - llm]: 0.24999427795410156 - 0.13384556770324707
[2025-06-25 17:57:17,901]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.6502059: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 17:57:32,581]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 17:57:32,581]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 17:57:32,582]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.582554: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: m\u00ecnh v\u1eabn ch\u01b0a th\u1ea5y ai li\u00ean h\u1ec7 h\u1ed7 tr\u1ee3 gi\u00fap m\u00ecnh \u1ea1\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 17:57:32,952]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.582554: ["1"]; time analysis [real - llm]: 0.3694937229156494 - 0.23784708976745605
[2025-06-25 17:57:32,952]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.582554: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-30 10:21:50,053]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-30 10:21:50,054]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-30 10:21:50,055]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.0554845: {"questions": ["Cho C\u00c2U H\u1eceI c\u1ee7a kh\u00e1ch h\u00e0ng tr\u00ean m\u1ea1ng x\u00e3 h\u1ed9i v\u00e0 danh s\u00e1ch c\u00e1c ch\u1ee7 th\u1ec3 nh\u01b0 sau:\n\nC\u00c2U H\u1eceI: m\u00ecnh v\u1eabn ch\u01b0a th\u1ea5y ai li\u00ean h\u1ec7 h\u1ed7 tr\u1ee3 gi\u00fap m\u00ecnh \u1ea1\n\nDanh s\u00e1ch c\u00e1c CH\u1ee6 TH\u1ec2:\n    + T\u00e0i kho\u1ea3n\n    + Th\u1ebb\n    + Th\u1ebb t\u00edn d\u1ee5ng\n    + Th\u1ebb ATM\n    + Th\u1ebb chip\n    + Th\u1ebb t\u1eeb\n    + Th\u1ebb Visa Eco\n    + \u0110i\u1ec3m th\u01b0\u1edfng\n    + M\u1eadt kh\u1ea9u\n    + M\u00e3 PIN\n    + Internet Banking\n    + Mobile Banking\n    + SMS Banking\n    + SoftPOS\n    + V\u1ed1n vay\n    + Sinh l\u1eddi t\u1ef1 \u0111\u1ed9ng\n    + Chi nh\u00e1nh\n    + Ph\u00f2ng giao d\u1ecbch\n    + Tuy\u1ec3n d\u1ee5ng\n    + Vi\u1ec7c l\u00e0m\n    + App/\u1ee8ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n    + Loa b\u00e1n h\u00e0ng\n    + Chuy\u1ec3n ti\u1ec1n\n    + Giao d\u1ecbch\n    + Ti\u1ec1n\n    + H\u1ea1n m\u1ee9c\n    + Bi\u1ec3u ph\u00ed\n    + Ph\u00ed duy tr\u00ec\n    + S\u1ed1 d\u01b0\n    + Sao k\u00ea\n    + S\u1ed1 t\u00e0i kho\u1ea3n\n    + S\u1ed1 \u0111i\u1ec7n tho\u1ea1i\n    + Email\n    + QR code\n    + POS\n    + Voucher\n    + L\u1eeba \u0111\u1ea3o\n    + Th\u1eddi gian ho\u1ea1t \u0111\u1ed9ng\n    + T\u1ed5ng \u0111\u00e0i\n    + CMND\n    + CCCD\n    + H\u1ed9 chi\u1ebfu\n    + Th\u00f4ng tin c\u00e1 nh\u00e2n\n    + M\u00e3 l\u1ed7i\n    + Ch\u1ee9ng kho\u00e1n\n    + Techcom Securities\n    + V\u1ed1n l\u01b0u \u0111\u1ed9ng\n    + L\u1ed7i \u1ee9ng d\u1ee5ng\n    + Doanh nghi\u1ec7p\n\nV\u1edbi vai tr\u00f2 l\u00e0 chuy\u00ean gia ph\u00e2n t\u00edch, ph\u00e2n lo\u1ea1i th\u00f4ng tin, h\u00e3y th\u1ef1c ki\u1ec3m tra n\u1ed9i dung C\u00c2U H\u1eceI c\u00f3 \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean hay kh\u00f4ng, n\u1ebfu kh\u00f4ng \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch CH\u1ee6 TH\u1ec2 tr\u00ean tr\u1ea3 v\u1ec1 0 v\u00e0 N\u1ebfu \u0111\u1ec1 c\u1eadp tr\u1ef1c ti\u1ebfp t\u1edbi b\u1ea5t k\u1ef3 ch\u1ee7 th\u1ec3 n\u00e0o trong danh s\u00e1ch ch\u1ee7 th\u1ec3 tr\u00ean tr\u1ea3 v\u1ec1 1\n        \nCh\u00fa \u00fd:\n- Ch\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-30 10:21:50,240]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.0554845: ["1"]; time analysis [real - llm]: 0.18523216247558594 - 0.13489270210266113
[2025-06-30 10:21:50,241]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.0554845: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
