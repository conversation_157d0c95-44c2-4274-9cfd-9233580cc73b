import json

file_path = "/hdd/thienthan/chatbot-rasa/database/data/customer.json"

class CustomerClient():
    def __init__(self, file_path = "/hdd/thienthan/chatbot-rasa/database/data/customer.json"):
        self.file_path = file_path

    def get_customers(self):
        try:
            with open(file_path) as f:
                customer = json.load(f)
        except Exception as ex:
            customer = []
        return customer

    def save_customer(self, customers):
        with open(file_path, 'w') as f:
            json.dump(customers, f, ensure_ascii=False)

    def add_customer(self, customer):
        print(customer)
        customers = self.get_customers()
        customers.append(customer)
        self.save_customer(customers)

    def update_customer(self, data_update, key):
        customers = self.get_customers()
        for customer in customers:
            if customer["key"] == key:
                customer.update(data_update)
        self.save_customer(customers)

    def delete_customer(self, keys):
        customers = self.get_customers()
        delete_ids = []
        for i,customer in enumerate(customers):
            if customer["key"] in keys:
                delete_ids.append(i)
        customers = [x for i,x in enumerate(customers) if i not in delete_ids]
        self.save_customer(customers)

    def get_config_detail(customer_id, url_detail):

        return {}