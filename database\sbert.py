from sentence_transformers import SentenceTransformer
from typing import List
import torch 
import numpy as np
from sklearn.preprocessing import normalize as normalize_func
class SentenceBert(): 
    def __init__(self, sbert_path, device: int = -1): 
        self.device = torch.device('cpu') if device == -1 or not torch.cuda.is_available() else torch.device(f'cuda:{str(device)}') 
        print("torch.cuda.is_available(): ",torch.cuda.is_available() , self.device) 
        self.model = SentenceTransformer(sbert_path, device=self.device) 
        # self.model.eval()
        
    def embed_sentences(self, data: List[str], normalize: bool = True) -> torch.Tensor: 
        embeddings = self.model.encode(data, batch_size=2, device=self.device, show_progress_bar=False) 
 
        if normalize: 
            # return  np.array([embedding /  np.linalg.norm(embedding) for embedding in embeddings]).astype("float32")
            return  normalize_func(embeddings, axis = 1).astype("float32") 
        return embeddings

if __name__ == '__main__':
    sbert_path = "/home1/vietle/QnA-core/QnA/models/vi_models/sbert_triplet"
    model = SentenceBert(sbert_path, device=3)
    
    text ="""NHỮNG BIỂU HIỆN NGUY HIỂM: Nếu có những biểu hiện sau đây cần đưa ngay trẻ/người tiêm chủng đến ngay bệnh viện gần nhất: 

• Sốt trên 39°C 

- Co giật hay mệt lả, lừ đừ, không có phản ứng khi được gọi -Tím tái, khó thở, thở rít, rút lõm 

lồng ngực khi thờ - Trẻ quấy khóc dữ dội, khóc thét kéo dài •Trẻ ăn/bú kém cùng các phản ứng 

thường gặp: sốt nhẹ, quấy khóc, 

phát ban.... kéo dài trên 1 ngày 

) Nếu trẻ có biểu hiện: một là, cơ tay chân mềm nhão so với bình thường, da tái xanh hay tím tái, phản ứng chậm hoặc không phản ứng khi gọi. Trong trường hợp này cần lay gọi trẻ, kích thích bằng cách vuốt lưng dọc 2 bên cột sống hoặc kích thích vào lòng bàn chân của trẻ và tìm cách nhanh nhất đưa trẻ đến bệnh viện để được cấp cứu. 

là Cần chủ động hỏi ý kiến của nhân viên y tế tại Trung tâm tiêm chủng hoặc chuyên gia về tiêm chủng nếu người được tiêm hoặc người chăm sóc trẻ chưa rõ các thông tin. Nên tìm hiểu trước về các bệnh viện gần nhất có khả năng cấp cứu và xử trí phản ứng sau tiêm. 

) Những phản ứng nặng sau tiêm chủng thường rất hiếm gặp và không gây nguy hiểm nếu được phát hiện và xử trí kịp thời. An toàn tiêm chủng không đơn thuần là chất lượng vắc xin, sự tuân thủ quy trình của nhân viên y tế mà còn bao gồm cả sự theo dõi, chăm sóc của gia đình sau khi tiêm chủng cho trẻ/người đi tiêm. 

HOTLINE VNVC: 028 7300 6595 

SỐ ĐIỆN THOẠI CẤP CỨU: 115"""
    query = "Những biểu hiện nguy hiểm sau tiêm chủng"
    text_emb = f"{text}".lower()
    data = [text_emb]
    a = model.embed_sentences(data)
    print(a)