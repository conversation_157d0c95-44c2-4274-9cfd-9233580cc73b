import pandas as pd
import os
from ruamel.yaml import YAM<PERSON>
from collections import defaultdict
import json
from create_data.create_intent import create_question_keywords_json

# Đọc file Excel
df = pd.read_excel(r"C:\Users\<USER>\PycharmProjects\chatbot-rasa\create_data\data\kịch bản TCB.xlsx")

# Cài YAML writer
yaml = YAML()
yaml.preserve_quotes = True
yaml.width = 4096  # Tránh ngắt dòng tự động

# Khởi tạo dữ liệu
responses = {}
stories = []
intents = []
entities_set = set()

# Nhóm các examples theo intent
examples_by_intent = defaultdict(list)

# Đọc dữ liệu từ Excel và nhóm theo intent
for idx, row in df.iterrows():
    intent_name = f"intent_{idx + 1}"

    if intent_name not in intents:
        intents.append(intent_name)

    original_keywords_excel_str = str(row['<Từ khóa trong câu>: <Loại từ khóa>']) if pd.notna(
        row['<Từ khóa trong câu>: <Loại từ khóa>']) else ""
    answer_excel = str(row['Câu trả lời']) if pd.notna(
        row['Câu trả lời']) else "Xin lỗi, tôi chưa có câu trả lời phù hợp."

    current_intent_generated_examples = []

    # Xử lý thực thể từ cột Excel gốc để điền vào entities_set
    processed_excel_keywords_for_entities = original_keywords_excel_str.strip()
    if ":" in processed_excel_keywords_for_entities:
        parts = processed_excel_keywords_for_entities.rsplit(':', 1)
        potential_entity_type = parts[1].strip()
        if potential_entity_type and potential_entity_type.isalnum() and not any(c in '[]()' for c in potential_entity_type):
            entities_set.add(potential_entity_type)

    # Gọi hàm create_question_keywords_json để tạo ví dụ
    gemini_json_output_str = create_question_keywords_json(
        keywords=original_keywords_excel_str, 
        answer=answer_excel,
        quantity=30
    )

    if gemini_json_output_str:
        try:
            gemini_data = json.loads(gemini_json_output_str)
            
            if isinstance(gemini_data.get("original_keywords"), list):
                for kw in gemini_data["original_keywords"]:
                    if kw and kw.strip():
                        current_intent_generated_examples.append(kw.strip())
            
            if isinstance(gemini_data.get("new_keywords"), list):
                for kw in gemini_data["new_keywords"]:
                    if kw and kw.strip():
                        current_intent_generated_examples.append(kw.strip())

            if isinstance(gemini_data.get("generated_items"), list):
                for item in gemini_data["generated_items"]:
                    question = item.get("question", "").strip()
                    if question:
                        current_intent_generated_examples.append(question)
        except json.JSONDecodeError as e:
            print(f"Lỗi khi parse JSON từ Gemini cho intent {intent_name}: {e}. Dữ liệu: {gemini_json_output_str}")

    # Fallback: Nếu không có ví dụ nào từ Gemini, thêm từ khóa gốc từ Excel
    if not current_intent_generated_examples:
        cleaned_original_excel_kw = original_keywords_excel_str.strip()
        if cleaned_original_excel_kw:
            fallback_example = cleaned_original_excel_kw.split(':')[0].strip()
            if fallback_example:
                 current_intent_generated_examples.append(fallback_example)
            else:
                current_intent_generated_examples.append("Tôi cần hỏi thông tin.")
        else:
            current_intent_generated_examples.append("Tôi cần hỏi thông tin.")
    
    # Thêm các ví dụ đã tạo (loại bỏ trùng lặp) vào danh sách của intent
    examples_by_intent[intent_name].extend(list(dict.fromkeys(current_intent_generated_examples)))

    # Response
    response_key = f"utter_{intent_name}"
    if response_key not in responses:
        responses[response_key] = [{"text": answer_excel}]

    # Story
    story_id = f"{intent_name}_story"
    if not any(s['story'] == story_id for s in stories):
        stories.append({
            "story": story_id,
            "steps": [
                {"intent": intent_name},
                {"action": response_key}
            ]
        })

# Đảm bảo thư mục tồn tại
os.makedirs("data", exist_ok=True)

# Ghi file nlu_n.yml
with open("data/nlu.yml", "w", encoding="utf-8") as f:
    f.write("version: '3.1'\n")
    f.write("nlu:\n")
    for intent_name_loop in intents:
        if intent_name_loop in examples_by_intent and examples_by_intent[intent_name_loop]:
            f.write(f"- intent: {intent_name_loop}\n")
            f.write("  examples: |\n")
            for example in examples_by_intent[intent_name_loop]:
                if example and example.strip(): 
                    f.write(f"    - {example.strip()}\n")

# Ghi file domain.yml
with open("domain.yml", "w", encoding="utf-8") as f:
    domain_content = {
        "version": "3.1",
        "intents": intents,
        "entities": list(entities_set),
        "responses": responses,
        "actions": [],
        "session_config": {
            "session_expiration_time": 60,
            "carry_over_slots_to_new_session": True
        }
    }
    yaml.dump(domain_content, f)

# Ghi file stories.yml
with open("data/stories.yml", "w", encoding="utf-8") as f:
    stories_content = {
        "version": "3.1",
        "stories": stories
    }
    yaml.dump(stories_content, f)

print("✅ Hoàn tất tạo các file RASA từ Excel (sử dụng Gemini để tạo examples).")