import os
import json
import time

# Th<PERSON> mục chứa các file JSON
input_folder = "./requests"
output_file = "output.txt"

# Hàm kiểm tra nếu text chỉ toàn là số
def is_numeric(text):
    return text.strip().isdigit()

# <PERSON><PERSON> sách để chứa tất cả text hợp lệ từ các file
all_texts = []
start_time = time.time()
# Duyệt tất cả các file trong thư mục
for filename in os.listdir(input_folder):
    if filename.endswith(".json") or filename.endswith(".jsonl"):
        filepath = os.path.join(input_folder, filename)
        with open(filepath, "r", encoding="utf-8") as f:
            try:
                content = json.load(f)
                # Nếu là một danh sách (trong file .jsonl hoặc nhiều object)
                if isinstance(content, list):
                    items = content
                else:
                    items = [content]

                for data in items:
                    # Lấy text ch<PERSON>h nếu hợp lệ
                    if "text" in data and not is_numeric(data["text"]):
                        all_texts.append(data["text"])

                    # L<PERSON>y trong history nếu có
                    for item in data.get("history", []):
                        text = item.get("text", "")
                        if not is_numeric(text):
                            all_texts.append(text)
            except Exception as e:
                print(f"Lỗi khi xử lý file {filename}: {e}")

# Ghi tất cả text hợp lệ vào file output.txt
with open(output_file, "w", encoding="utf-8") as f:
    for text in all_texts:
        f.write(text + "\n")

end_time = time.time()

print(f"✅ Đã lưu {len(all_texts)} dòng text hợp lệ vào '{output_file}'")
print(f"Time x lý: {end_time - start_time}")