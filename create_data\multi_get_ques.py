import os
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

input_folder = "./requests"
output_file = "output.txt"

# Hàm kiểm tra nếu text chỉ là số
def is_numeric(text):
    return text.strip().isdigit()

# Hàm xử lý từng file riêng lẻ
def process_file(filepath):
    texts = []
    try:
        with open(filepath, "r", encoding="utf-8") as f:
            content = json.load(f)

            # Nếu là list các object (vd: .jsonl giả)
            if isinstance(content, list):
                items = content
            else:
                items = [content]

            for data in items:
                if "text" in data and not is_numeric(data["text"]):
                    texts.append(data["text"])

                for item in data.get("history", []):
                    text = item.get("text", "")
                    if not is_numeric(text):
                        texts.append(text)
    except Exception as e:
        print(f"Lỗi khi xử lý file {filepath}: {e}")
    return texts

# Thu thập tất cả các file JSON cần xử lý
file_list = [
    os.path.join(input_folder, filename)
    for filename in os.listdir(input_folder)
    if filename.endswith(".json") or filename.endswith(".jsonl")
]

all_texts = []

start_time = time.time()

# Dùng ThreadPoolExecutor để xử lý đồng thời
with ThreadPoolExecutor(max_workers=8) as executor:  # Điều chỉnh số lượng luồng tại đây
    future_to_file = {executor.submit(process_file, file): file for file in file_list}

    for future in as_completed(future_to_file):
        result = future.result()
        all_texts.extend(result)

# Ghi kết quả ra file
with open(output_file, "w", encoding="utf-8") as f:
    for text in all_texts:
        f.write(text + "\n")

end_time = time.time()

print(f"✅ Đã xử lý {len(file_list)} file, tổng {len(all_texts)} dòng text được lưu vào '{output_file}'")
print(f"Thời gian xử lý: {end_time - start_time}")
