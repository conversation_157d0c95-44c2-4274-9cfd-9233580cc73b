from ic_libs.ic_rabbit import ICRabbitMQ

def init_rabbit_queue(usr, passw, host, vir_host, queue_name, durable, max_priority, exchange=""):
    connection_ = ICRabbitMQ(host, vir_host, usr, passw)
    # connection.init_connection()
    channel_ = connection_.init_queue(queue_name, exchange=exchange, durable=durable, max_priority=max_priority)
    return channel_, connection_, queue_name

# queue_chatbot_message_params = {
#     "UserName": "long.nguyen",
#     "Password": "1",
#     "HostName": "**********",
#     "VirtualHost": "chatbot",
#     "Queue": "chatbot-message"
# }

queue_chatbot_message_params = {
    "UserName": "chatbot-dev-rnd",
    "Password": "1qa2ws#ED$RF",
    "HostName": "**********",
    "VirtualHost": "chatbot",
    "Queue": "chatbot-message-rasa"
}

queue_chatbot_message_params_dev = {
    "UserName": "chatbot-dev-rnd",
    "Password": "1qa2ws#ED$RF",
    "HostName": "**********",
    "VirtualHost": "chatbot",
    "Queue": "chatbot-message-dev"
}

queue_chatbot_message_params_llm = {
    "UserName": "chatbot-dev-rnd",
    "Password": "1qa2ws#ED$RF",
    "HostName": "**********",
    "VirtualHost": "chatbot",
    "Queue": "chatbot-message"
}