# Playbook Vận hành Module Chatbot Ngữ cảnh LLM

Tài liệu này hướng dẫn các quy trình cơ bản để vận hành, bảo trì và phát triển module chatbot có khả năng gộp tin nhắn để hiểu ngữ cảnh.

## 1. <PERSON><PERSON><PERSON> tiêu & Tổng quan

*   **M<PERSON><PERSON> tiêu:** Cải thiện chất lượng hội thoại của chatbot bằng cách gộp các tin nhắn gần nhau của cùng một người dùng thành một ngữ cảnh duy nhất trước khi gửi đến Mô hình Ngôn ngữ lớn (LLM).
*   **Lợi ích:**
    *   **Hiểu ngữ cảnh tốt hơn:** LLM nhận được toàn bộ ý của người dùng thay vì các mẩu tin nhắn rời rạc (ví dụ: "tôi muốn khóa thẻ" và "thẻ tín dụng" thành "tôi muốn khóa thẻ tín dụng").
    *   **Giảm chi phí & độ trễ:** Giảm số lượng cuộc gọi API đến LLM.
    *   **Trải nghiệm người dùng tự nhiên hơn:** Bot có thể chờ người dùng gõ xong ý của họ trước khi trả lời.

## 2. Kiến trúc & Luồng hoạt động

Hệ thống bao gồm một `Consumer` (người tiêu thụ tin nhắn) và một `Processor` (bộ xử lý) hoạt động bất đồng bộ.

```mermaid
graph TD
    subgraph RabbitMQ
        A[Hàng đợi tin nhắn]
    end

    subgraph Consumer (consumer_chatbot_llm_context.py)
        B(1. Nhận tin nhắn) --> C{2. Gửi đến Processor};
        C --> D[Giữ lại message, không ACK];
        E(6. Nhận callback từ Processor) --> F[Gửi phản hồi & Lưu kết quả];
        F --> G(7. ACK tất cả message trong batch);
    end

    subgraph Processor (process_messages_llm_context.py)
        H(3. Thêm tin nhắn vào Batch) --> I{Batch đầy hoặc Timeout?};
        I -- No --> J[Reset Timer & Chờ];
        I -- Yes --> K(4. Gộp tin nhắn & Gọi LLM);
        K --> L(5. Gọi Response Callback);
    end

    A --> B;
    C --> H;
    L --> E;
    G --> A;
```

*   **Bước 1 & 2:** `Consumer` nhận một tin nhắn từ RabbitMQ và ngay lập tức chuyển nó cho `LLMChatProcessor` để xử lý.
*   **Bước 3:** `Processor` thêm tin nhắn vào một "batch" (lô) dành riêng cho người dùng đó. Nó sẽ hủy và đặt lại một bộ đếm thời gian (timer) mỗi khi có tin nhắn mới.
*   **Bước 4:** Khi bộ đếm thời gian kết thúc (người dùng ngừng nhắn tin trong một khoảng thời gian) hoặc khi số lượng tin nhắn trong batch đạt giới hạn, `Processor` sẽ gộp tất cả các tin nhắn lại và gửi đến LLM để xử lý.
*   **Bước 5 & 6:** Sau khi có kết quả từ LLM, `Processor` gọi một hàm `response_callback` đã được `Consumer` đăng ký trước đó. Hàm này chịu trách nhiệm gửi câu trả lời cuối cùng cho người dùng và lưu kết quả.
*   **Bước 7 (Quan trọng):** Sau khi đã xử lý và gửi phản hồi thành công, `Consumer` mới gửi tín hiệu xác nhận (ACK) cho RabbitMQ rằng tất cả các tin nhắn trong batch đã được xử lý xong. Điều này đảm bảo không một tin nhắn nào bị mất nếu hệ thống gặp sự cố.

## 3. Các thành phần chính

*   **`consumer_chatbot_llm_context.py`**:
    *   **Vai trò:** Là cổng vào của hệ thống.
    *   **Trách nhiệm:**
        *   Kết nối và lắng nghe tin nhắn từ RabbitMQ.
        *   Khởi tạo `LLMChatProcessor` và đăng ký hàm `send_response_to_platform` làm callback.
        *   Chuyển tiếp tin nhắn đến `Processor`.
        *   Xử lý kết quả trả về từ callback (gửi tin, lưu log) và thực hiện `ack` tin nhắn.
*   **`function/process_messages_llm_context.py`**:
    *   **Vai trò:** Bộ não xử lý logic gộp và hiểu ngữ cảnh.
    *   **Trách nhiệm:**
        *   Quản lý các batch tin nhắn cho từng người dùng (`user_batches`).
        *   Quản lý vòng đời của timer cho mỗi batch.
        *   Gộp các tin nhắn thành một văn bản duy nhất.
        *   Gọi các dịch vụ LLM (phân loại, xác thực).
        *   Gọi `response_callback` khi có kết quả.
*   **Các tham số cấu hình quan trọng (`DEFAULT_CONFIG`):**
    *   `batch_timeout_seconds`: (Mặc định: `25.0`) Thời gian (giây) chờ đợi kể từ tin nhắn cuối cùng trước khi xử lý batch. Đây là tham số quan trọng nhất ảnh hưởng đến độ trễ và khả năng gộp tin nhắn.
    *   `max_batch_size`: (Mặc định: `5`) Số lượng tin nhắn tối đa trong một batch. Nếu đạt đến số này, batch sẽ được xử lý ngay lập tức.
    *   `max_message_gap_seconds`: (Mặc định: `30.0`) Nếu khoảng cách thời gian giữa hai tin nhắn liên tiếp lớn hơn giá trị này, hệ thống sẽ coi đó là một cuộc hội thoại mới và xử lý batch cũ ngay lập tức.

## 4. Quy trình Vận hành & Bảo trì

*   **Điều chỉnh thời gian gộp tin nhắn:**
    *   **Mục đích:** Cân bằng giữa việc chờ đợi để có ngữ cảnh đầy đủ và việc trả lời người dùng nhanh chóng.
    *   **Cách làm:** Thay đổi giá trị của `batch_timeout_seconds` trong `DEFAULT_CONFIG` của file `process_messages_llm_context.py`. Tăng giá trị để chờ lâu hơn, giảm để trả lời nhanh hơn.
*   **Xử lý khi Bot không trả lời (do batch):**
    *   **Hiện tượng:** Người dùng gửi tin nhắn nhưng không nhận được phản hồi ngay.
    - **Kiểm tra:** Xem log của `consumer_chatbot_llm_context.py` và `process_messages_llm_context.py`.
    *   **Nguyên nhân có thể:**
        *   Tin nhắn đã được đưa vào batch và đang chờ timeout. Log sẽ có dòng `Started new batch for user...` hoặc `Added message to existing batch...`.
        *   Lỗi xảy ra trong `response_callback`. Kiểm tra log `CALLBACK TRIGGERED` và các log lỗi theo sau.
*   **Xử lý tắt/khởi động lại Consumer:**
    *   `consumer_chatbot_llm_context.py` có cơ chế `force_process_pending_batches()` được gọi khi tiến trình bị dừng (ví dụ: `CTRL+C`).
    *   **Mục đích:** Đảm bảo tất cả các tin nhắn đang được giữ trong các batch sẽ được xử lý nốt trước khi consumer tắt hoàn toàn, tránh mất dữ liệu.

## 5. Phân tích sâu & Cải tiến (Quan trọng)

Kiến trúc hiện tại đã giải quyết được vấn đề gửi dữ liệu và logic gộp tin nhắn. Tuy nhiên, để hệ thống hoạt động ổn định và tin cậy trong môi trường production, cần phải giải quyết một vấn đề cốt lõi: **Đảm bảo không mất tin nhắn (Message Durability) khi consumer gặp sự cố.**

*   **Vấn đề:** Trong `consumer_chatbot_llm_context.py`, hàm `on_message` sử dụng `async with message.process()`. Cơ chế này sẽ tự động `ack` (xác nhận) tin nhắn với RabbitMQ ngay khi khối `with` kết thúc mà không có lỗi. Điều này có nghĩa là, một tin nhắn được `ack` ngay sau khi nó được đưa vào `LLMChatProcessor`, *trước khi* batch chứa nó được xử lý xong. Nếu consumer sập trong khoảng thời gian này, tin nhắn đó sẽ bị mất vĩnh viễn.

*   **Giải pháp đúng:** Chỉ `ack` một tin nhắn sau khi đã có phản hồi cuối cùng cho nó. Điều này đòi hỏi một sự phối hợp chặt chẽ hơn giữa `Consumer` và `Processor`.

    1.  **Consumer (`on_message`)**: Không `ack` ngay. Thay vào đó, nó sẽ truyền cả `message` (hoặc ít nhất là `message.delivery_tag`) vào cho `Processor`.
    2.  **Processor (`PendingMessage`)**: Lưu lại `delivery_tag` của mỗi tin nhắn.
    3.  **Processor (`_process_user_batch`)**: Sau khi xử lý xong, gọi `response_callback` và truyền cả danh sách các `delivery_tag` cần `ack`.
    4.  **Consumer (`send_response_to_platform`)**: Sau khi gửi phản hồi thành công, sẽ duyệt qua danh sách và `ack` từng tin nhắn.

*   **Hành động:** Cần áp dụng các thay đổi trong mã nguồn để triển khai giải pháp này.