from icllmlib import LLM
from typing import Dict, List, Any
import json
import logging
import time
import os
from fastapi import HTTPException
from .intents_dict import intent_definitions

# Logger Setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

class IntentVerification:
    def __init__(
            self,
            app_code: str = "TCB",
            function_code: str = "tcb_intent_verify",
            model_llm: str = "llm-sea3.1",
            url_prompt: str = "https://staging.pontusinc.com/api/chatbot/v1/prompt/list",
            llm_name: str = "intent_verification",
            url_llm_api: str = "http://10.9.3.241:2033/api/llama3",
            base_dir_log: str = "logs/llm_logs",
            base_dir_post_process: str = "logs/llm_logs/post_process",
            base_dir_prompt: str = "logs/llm_logs/prompt",
            is_get_prompt_online: bool = False,
            time_sleep: int = 5,
            is_log: bool = False,
    ):
        # Create necessary directories
        os.makedirs(base_dir_log, exist_ok=True)
        os.makedirs(base_dir_post_process, exist_ok=True)
        os.makedirs(base_dir_prompt, exist_ok=True)
        
        try:
            self.llm_engine = LLM(
                app_code=app_code,
                function_code=function_code,
                model_llm=model_llm,
                url_prompt=url_prompt,
                llm_name=llm_name,
                url_llm_api=url_llm_api,
                url_get_llm_api="",
                base_dir_log=base_dir_log,
                base_dir_post_process=base_dir_post_process,
                base_dir_prompt=base_dir_prompt,
                is_log=True,
                is_show_console=False,
                is_get_prompt_online=is_get_prompt_online,
            )
            logger.info("LLM engine initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize LLM engine: {e}", exc_info=True)
            raise

        if is_get_prompt_online:
            try:
                self.llm_engine.get_prompt_frequency(time_sleep=time_sleep)
                logger.info("Successfully got prompt frequency")
            except Exception as e:
                logger.error(f"Failed to get prompt frequency: {e}", exc_info=True)

    def verify_intent_relevance(self, question: str, intent_name: str) -> Dict[str, Any]:
        """
        Verify if a question is directly related to a specific intent domain.
        
        Args:
            question (str): The user's question to verify
            intent_name (str): The intent name to check against in intent_definitions
            
        Returns:
            Dict[str, Any]: A dictionary containing verification result and metadata.
        """
        start_time = time.time()
        
        if not question or question.strip() == "":
            logger.warning("Empty question received")
            return self._create_verification_response(
                question=question,
                intent_name=intent_name,
                result=0,
                start_time=start_time,
                status="failed: empty question"
            )
            
        if not intent_name or intent_name.strip() == "":
            logger.warning("Empty intent name received")
            return self._create_verification_response(
                question=question,
                intent_name=intent_name,
                result=0,
                start_time=start_time,
                status="failed: empty intent name"
            )

        intent_domain = intent_definitions.get(intent_name.strip())
        if not intent_domain:
            logger.error(f"Intent name '{intent_name}' not found in intent_definitions.")
            return self._create_verification_response(
                question=question,
                intent_name=intent_name,
                result=0,
                start_time=start_time,
                status=f"failed: intent name '{intent_name}' not found"
            )

        try:
            # Prepare prompt data with both question and intent domain
            prompt_data = {
                "$$question": question.strip(),
                "$$intent_domain": intent_domain.strip()
            }
            
            llm_start_time = time.time()
            
            response = self.llm_engine.generate(
                prompt_data, 
                temperature=0.1, 
                max_decoding_length=64,  # Shorter response needed for 0/1
                repetition_penalty=1.1
            )
            
            llm_response_time = time.time() - llm_start_time
            logger.info(f"LLM response time: {llm_response_time:.2f}s")
            
            # Parse LLM response to get 0 or 1
            verification_result = self._parse_verification_result(response)
            
            logger.info(f"Intent verification result: {verification_result} for question: '{question}' and domain: '{intent_domain}'")
            
            return self._create_verification_response(
                question=question,
                intent_name=intent_name,
                result=verification_result,
                start_time=start_time,
                status="success",
                llm_response_time=llm_response_time
            )

        except Exception as e:
            logger.error(f"Error during intent verification: {e}", exc_info=True)
            return self._create_verification_response(
                question=question,
                intent_name=intent_name,
                result=0,
                start_time=start_time,
                status=f"failed: {str(e)}"
            )

    def _parse_verification_result(self, response: List[Dict]) -> int:
        """
        Parse LLM response to extract 0 or 1 result.
        
        Args:
            response: LLM response list
            
        Returns:
            int: 0 or 1 based on verification
        """
        if not response or not response[0].get('answer_norm'):
            logger.warning("No valid response from LLM")
            return 0
            
        answer = response[0]['answer_norm'].strip().lower()
        logger.debug(f"Raw LLM answer: {answer}")
        
        # Try to extract 0 or 1 from the response
        if '1' in answer and '0' not in answer:
            return 1
        elif '0' in answer and '1' not in answer:
            return 0
        elif answer in ['yes', 'true', 'có', 'đúng', 'liên quan']:
            return 1
        elif answer in ['no', 'false', 'không', 'sai', 'không liên quan']:
            return 0
        else:
            # If unclear, default to 0 (not related)
            logger.warning(f"Unclear LLM response, defaulting to 0: {answer}")
            return 0

    def _create_verification_response(
        self, 
        question: str, 
        intent_name: str, 
        result: int, 
        start_time: float, 
        status: str,
        llm_response_time: float = 0
    ) -> Dict[str, Any]:
        """Create a standardized verification response."""
        intent_domain = intent_definitions.get(intent_name.strip())
        return {
            "verification_result": result,
            "question": question,
            "intent_name": intent_name,
            "intent_domain": intent_domain,
            "is_directly_related": bool(result),
            "status": status,
            "metadata": {
                "processing_time": time.time() - start_time,
                "llm_response_time": llm_response_time,
                "timestamp": time.time()
            }
        }




if __name__ == "__main__":
    try:
        verifier = IntentVerification()
        
        # Single test case
        test_question = "phải làm gì để khắc phục ạ?"
        test_intent_name = "intent_khoa_the"
        
        result_dict = verifier.verify_intent_relevance(test_question, test_intent_name)
        
        print("Intent Verification Result:")
        print("=" * 40)
        print(f"Question: {result_dict['question']}")
        print(f"Intent Name: {result_dict['intent_name']}")
        print(f"Intent Domain: {result_dict['intent_domain']}")
        print(f"Verification Result: {result_dict['verification_result']} ")
        print(f"Status: {result_dict['status']}")
        print(f"Processing Time: {result_dict['metadata']['processing_time']:.2f}s")
        if result_dict['metadata']['llm_response_time'] > 0:
            print(f"LLM Response Time: {result_dict['metadata']['llm_response_time']:.2f}s")

    except Exception as e:
        logger.error(f"Test failed: {e}", exc_info=True)