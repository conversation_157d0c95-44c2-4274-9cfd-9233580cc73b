import asyncio
import json
import logging
from typing import Dict, Any

# Thư viện cần thiết: pip install aio-pika
import aio_pika
from aio_pika.abc import AbstractIncomingMessage

# Import các thành phần từ project củ<PERSON> bạn
from rabbit_common import queue_chatbot_message_params_llm, queue_chatbot_message_params_dev
from function.process_messages_llm_context import LLMChatProcessor, DEFAULT_CONFIG as LLM_DEFAULT_CONFIG
from function.save_result import save_result_chatbot

# Cấu hình logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# Khởi tạo processor một lần duy nhất
try:
    chat_processor = LLMChatProcessor(config=LLM_DEFAULT_CONFIG)
    logger.info("LLMChatProcessor initialized successfully.")
except Exception as e:
    logger.critical("LLMChatProcessor failed to initialize", exc_info=True)
    exit(1)

async def on_message(message: AbstractIncomingMessage) -> None:
    """Callback xử lý mỗi tin nhắn từ RabbitMQ."""
    async with message.process(ignore_processed=True):
        try:
            body_data = json.loads(message.body.decode("utf-8"))
            is_web_req_flag = body_data.get("is_web_request", False)

            # Giao toàn bộ việc xử lý cho processor.
            # Processor sẽ tự quyết định gộp hay xử lý ngay.
            # Nếu gộp, nó sẽ trả về "silent" response và không làm gì cả.
            # Response thật sự sẽ được gửi qua callback sau này.
            await chat_processor.process_message(body_data, is_web_request=is_web_req_flag)

        except json.JSONDecodeError:
            logger.error("JSON decode error. Dropping message.", extra={"body": message.body[:200]})
            await message.reject(requeue=False)
        except Exception:
            logger.error("Unhandled error processing message.", exc_info=True)
            await message.reject(requeue=False)

async def send_response_to_platform(user_id: str, response: Dict[str, Any]):
    """
    Hàm CALLBACK được gọi khi một batch được xử lý xong.
    Nhiệm vụ của nó là gửi response cuối cùng đến người dùng và lưu kết quả.
    """
    logger.info(f"🚀 CALLBACK TRIGGERED: Preparing to send response to user '{user_id}'.")
    
    # SỬA LỖI: Lấy ra dictionary action thực sự từ cấu trúc trả về.
    # Vì đây là consumer, response luôn được bọc trong key "action".
    action_payload = response.get("action")

    # Kiểm tra xem có nội dung để gửi không
    if action_payload and action_payload.get("actions"):
        try:
            logger.info(f"📤 Saving result for user '{user_id}': {action_payload.get('actions')}")
            
            # Chạy hàm đồng bộ trong một thread riêng để không block event loop
            # SỬA LỖI: Truyền `action_payload` (dữ liệu đã được giải nén) thay vì `response` (dữ liệu gốc)
            await asyncio.to_thread(save_result_chatbot, action_payload)
            
            # =================================================================
            # NƠI TÍCH HỢP LOGIC GỬI TIN NHẮN THỰC TẾ
            # Ví dụ: Gửi vào một queue khác tên là 'send_queue'
            # =================================================================
        except Exception as e:
            logger.error(f"Error in response callback for user '{user_id}': {e}", exc_info=True)
    else:
        # Thêm log chi tiết hơn để gỡ lỗi
        logger.warning(f"Skipping send for user '{user_id}' due to empty 'actions' in response. Full response: {json.dumps(response, ensure_ascii=False)}")

async def main() -> None:
    """Hàm chính khởi tạo và chạy consumer."""
    logger.info("Starting Asynchronous RabbitMQ Consumer...")

    # BẮT BUỘC: Thiết lập hàm callback cho processor
    chat_processor.set_response_callback(send_response_to_platform)

    # Lấy thông tin kết nối
    usr_name = queue_chatbot_message_params_llm["UserName"]
    password = str(queue_chatbot_message_params_dev["Password"])
    host = queue_chatbot_message_params_dev["HostName"]
    virtual_host = queue_chatbot_message_params_dev["VirtualHost"]
    queue_name = queue_chatbot_message_params_dev["Queue"]
    prefetch_count = queue_chatbot_message_params_dev.get('PrefetchCount', 10)

    connection = None
    while True:
        try:
            connection = await aio_pika.connect_robust(
                host=host,
                login=usr_name,
                password=password,
                virtualhost=virtual_host,
                heartbeat=60
            )
            
            async with connection:
                channel = await connection.channel()
                await channel.set_qos(prefetch_count=prefetch_count)
                
                queue = await channel.declare_queue(
                    queue_name, 
                    durable=True, 
                    arguments={"x-max-priority": 10}
                )
                
                logger.info(f"[*] Connected to RabbitMQ. Waiting for messages on '{queue_name}'. To exit press CTRL+C")
                
                await queue.consume(on_message)
                
                # Chạy mãi mãi
                await asyncio.Future()

        except aio_pika.exceptions.AMQPConnectionError as e:
            logger.warning(f"Connection error: {e}. Retrying in 5 seconds...")
            await asyncio.sleep(5)
        except Exception as e:
            logger.critical(f"An unexpected error occurred: {e}", exc_info=True)
            break
        finally:
            if connection:
                logger.info("Closing connection and processing pending batches...")
                # Xử lý các batch còn lại trước khi tắt
                await chat_processor.force_process_pending_batches()
                await connection.close()
                logger.info("Shutdown complete.")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Consumer stopped by user (CTRL+C).")