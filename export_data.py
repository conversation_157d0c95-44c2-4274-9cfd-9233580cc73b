import json
import pandas


def extract_data_from_log_entry(log_entry_text):
    processed_text = None
    intent_name = None

    processed_response_marker = "Processed Response:"
    try:
        start_index_marker = log_entry_text.index(processed_response_marker)
    except ValueError:
        return None

    json_text_block = log_entry_text[start_index_marker + len(processed_response_marker):]

    try:
        json_start_char_index = json_text_block.index('{')
        json_string = json_text_block[json_start_char_index:]
        data = json.loads(json_string)

        if "text" in data:
            processed_text = data["text"]

        if "intent" in data and isinstance(data["intent"], dict) and "name" in data["intent"]:
            intent_name = data["intent"]["name"]

        if processed_text is not None and intent_name is not None:
            return {
                "processed_text": processed_text,
                "intent_name": intent_name
            }
        else:
            return None

    except json.JSONDecodeError:
        return None
    except ValueError:
        return None
    except Exception as e:
        return None


def process_log_file(file_path):
    extracted_results = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"Lỗi: Không tìm thấy file '{file_path}'")
        return []

    log_entries = [entry.strip() for entry in content.split("---") if entry.strip()]

    if not log_entries:
        if content.strip() and "Processed Response:" in content:
            log_entries = [content.strip()]
        else:
            print("Không tìm thấy khối log nào hợp lệ trong file.")
            return []

    for i, entry_text in enumerate(log_entries):
        result = extract_data_from_log_entry(entry_text)
        if result:
            extracted_results.append(result)

    return extracted_results


def save_results_to_excel(results, excel_file_path):
    if not results:
        print("Không có dữ liệu để lưu vào Excel.")
        return

    try:
        df = pandas.DataFrame(results)
        df.rename(columns={
            'processed_text': 'Processed Text',
            'intent_name': 'Intent Name'
        }, inplace=True)

        df.to_excel(excel_file_path, index=False, engine='openpyxl')
        print(f"Đã lưu kết quả vào file: {excel_file_path}")

    except ImportError:
        print("Lỗi: Thư viện pandas hoặc openpyxl chưa được cài đặt.")
        print("Vui lòng cài đặt bằng lệnh: pip install pandas openpyxl")
    except Exception as e:
        print(f"Đã xảy ra lỗi khi lưu file Excel: {e}")


# --- Sử dụng ---
sample_data = """"""
log_file_path = "log_data.txt"
excel_output_path = "extracted_log_data.xlsx"

with open("chat_conversation_log_llm.txt", "r", encoding="utf-8") as f:
    sample_data = f.read()

with open(log_file_path, "w", encoding="utf-8") as f:
    f.write(sample_data)

results = process_log_file(log_file_path)

if results:
    print("\n--- Kết quả trích xuất ---")
    for res in results:
        print(f"Processed Text: \"{res['processed_text']}\", Intent Name: \"{res['intent_name']}\"")

    save_results_to_excel(results, excel_output_path)
else:
    print("Không có dữ liệu nào được trích xuất hoặc phù hợp với yêu cầu.")