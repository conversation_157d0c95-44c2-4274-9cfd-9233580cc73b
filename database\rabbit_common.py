import pika
import json


class ICRabbitMQ(object):
    def __init__(self, host, virtual_host, usr, passw, **kwargs):
        """
        Khởi tạo
        :param host: địa chỉ rabbitmq server
        :param virtual_host: virtual_host
        :param queue_name: tên queue
        :param usr: user rabbitmq server
        :param passw: password
        """
        self.host = host
        self.virtual_host = virtual_host
        self.user = usr
        self.passw = passw
        self.credentials = pika.PlainCredentials(usr, passw)
        self.connection = None
        self.kwargs = kwargs

    def init_connection(self):
        self.connection = \
            pika.BlockingConnection(
                pika.ConnectionParameters(host=self.host, virtual_host=self.virtual_host, credentials=self.credentials))

    def connection_close(self):
        self.connection.close()

    def connection_status(self):
        return self.connection.is_open

    def init_queue(self, queue_name, exchange="", exchange_type='fanout', durable=True, max_priority=-1):
        """
        khởi tạo queue
        :param exchange:
        :param queue_name: tên queue
        :param durable: true (Queue vẫn tồn tại nếu nhưng RabitMQ khởi động lại)
        :param max_priority: <PERSON><PERSON><PERSON> đ<PERSON> priority tối đa; None thì không xét priority;
                            khác None thì xét priority (tối đa priority = 10)
        :return: channel
        """
        if self.connection is None:
            self.init_connection()
        channel = self.connection.channel()
        if exchange == "" and queue_name != "":
            if max_priority == -1:
                channel.queue_declare(queue=queue_name, durable=durable)
            else:
                channel.queue_declare(queue=queue_name, durable=durable, arguments={'x-max-priority': max_priority})
        else:
            channel.exchange_declare(exchange=exchange, exchange_type='fanout', durable=durable)
        return channel

    @staticmethod
    def publish_message(channel, routing_key, body, priority=-1, delivery_mode=2, exchange=''):
        """
        run pushlish message
        :param channel: channel đã được tạo
        :param routing_key: key hoặc tên queue (nếu exchange = '')
        :param body: data push
        :param priority: mức ưu tiên
        :param delivery_mode: ??
        :param exchange: routing
        """
        if priority == -1:
            channel.basic_publish(exchange=exchange, routing_key=routing_key, body=json.dumps(body),
                                  properties=pika.BasicProperties(delivery_mode=delivery_mode))
        else:
            channel.basic_publish(exchange=exchange, routing_key=routing_key, body=json.dumps(body),
                                  properties=pika.BasicProperties(delivery_mode=delivery_mode, priority=priority))
        print("push done: ")

    @staticmethod
    def run_consummer(channel, queue_name, callback_func, is_ack=False):
        """
        run consumer
        :param channel: channel đã được tạo
        :param queue_name: tên queue
        :param callback_func: hàm callback được định nghĩa bởi người dùng
        :return:
        """
        print(" *wait message")

        def callback(ch, method, properties, body):
            body = json.loads(body.decode("utf-8"))
            if is_ack:
                ch.basic_ack(delivery_tag=method.delivery_tag)
                callback_func(body, properties)
            else:
                callback_func(body, properties)
                ch.basic_ack(delivery_tag=method.delivery_tag)

            
            print("receive done: ")

        channel.basic_qos(prefetch_count=10)
        channel.basic_consume(queue=queue_name, on_message_callback=callback)
        channel.start_consuming()

def init_rabbit_queue(usr, passw, host, vir_host, queue_name, durable, max_priority, exchange=""):
    connection = ICRabbitMQ(host, vir_host, usr, passw)
    connection.init_connection()
    channel = connection.init_queue(queue_name, exchange=exchange, durable=durable, max_priority=max_priority)
    return channel, connection, queue_name
                                                                    
def push_to_queue(data_push, params, connection=None, channel=None):
   
    usr_name = params["UserName"]
    password = str(params["Password"])
    host = params["HostName"]
    virtual_host = params["VirtualHost"]
    queue_name = params["Queue"]

    try:
        # print(f'[INFO] Connection is open: {rb_consumer.connection_status()}')
        if connection.connection_status():
            pass
        else:
            channel, connection, queue_name = init_rabbit_queue(usr_name, password, host,
                                                                virtual_host,
                                                                queue_name,
                                                                durable=True,
                                                                max_priority=10)
    except:
        channel, connection, queue_name = init_rabbit_queue(usr_name, password, host,
                                                            virtual_host,
                                                            queue_name,
                                                            durable=True,
                                                            max_priority=10)

    ICRabbitMQ.publish_message(channel, queue_name, data_push, delivery_mode=2, exchange='')  
    print(f'[INFO] push to queue {queue_name} done')                                                    

# CONFIG = {
#     "import_file_faq_params": {
#         "UserName": "long.nguyen",
#         'Password': 1,
#         "HostName": "**********",
#         "VirtualHost": "chatbot",
#         "Queue": "import-file-faq"
#     },
#     "chatbot_message_params": {
#         "UserName": "long.nguyen",
#         "Password": "1",
#         "HostName": "**********",
#         "VirtualHost": "chatbot",
#         "Queue": "chatbot-message-test"
#     },
#     "chatbot_app_async_params": {
#         "UserName": "long.nguyen",
#         "Password": "1",
#         "HostName": "**********",
#         "VirtualHost": "chatbot",
#         "Queue": "chatbot-app-async"
#     }
# }
CONFIG = {
    "import_file_faq_params": {
        "UserName": "long.nguyen",
        'Password': 1,
        "HostName": "**********",
        "VirtualHost": "chatbot",
        "Queue": "import-file-faq"
    },
    "chatbot_message_params": {
        "UserName": "chatbot-dev-rnd",
        "Password": "1qa2ws#ED$RF",
        "HostName": "**********",
        "VirtualHost": "chatbot",
        "Queue": "chatbot-message-rasa"
    },
    "chatbot_app_async_params": {
        "UserName": "long.nguyen",
        "Password": "1",
        "HostName": "**********",
        "VirtualHost": "chatbot",
        "Queue": "chatbot-app-async"
    }
}
