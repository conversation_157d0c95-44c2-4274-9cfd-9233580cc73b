import os
import json
import asyncio
import logging
import time
from copy import deepcopy
from typing import Dict, Any, Optional, List
import re

from .classify.question_classifier import ClassificationQuestion, classify_response
from .classify.double_check_by_llm import IntentVerification
from .classify.entity_verify import IntentVerification as EntityIntentVerification

# --- Inputs (Global configurations and paths) ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

DEFAULT_FALLBACK_MESSAGE = "Cảm ơn bạn đã cung cấp thông tin. Sẽ có chuyên viên tư vấn phản hồi bạn sớm ạ."

# --- Logger Setup ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

DEFAULT_CONFIG: Dict[str, Any] = {
    "classifier_app_code": "TCB",
    "classifier_function_code": "tcb_classify",
    "classifier_model_llm": "llm-sea3.1",
    "classifier_url_llm_api": "http://**********:2033/api/llama3",
    "classifier_is_get_prompt_online": False,
    "debug_write_record_json": True,
    "record_json_path": os.path.join(SCRIPT_DIR, "debug_record.json"),
    "intent_verification_app_code": "TCB",
    "intent_verification_function_code": "tcb_intent_verify",
    "intent_verification_model_llm": "llm-sea3.1",
    "intent_verification_url_llm_api": "http://**********:2033/api/llama3",
    "intent_verification_is_get_prompt_online": False
}

def convert_buttons_from_payload(buttons_payload: List[Dict[str, Any]], platform: str) -> List[Dict[str, Any]]:
    logger.info(f"Placeholder: convert_buttons_from_payload called with platform: {platform}, buttons_payload: {buttons_payload}")
    return buttons_payload

async def rewrite_question(question: str) -> str:
    return question

class LLMChatProcessor:
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = deepcopy(DEFAULT_CONFIG)
        if config:
            self.config.update(config)
        
        try:
            self.question_classifier = ClassificationQuestion(
                app_code=self.config.get("classifier_app_code", "TCB"),
                function_code=self.config.get("classifier_function_code", "tcb_classify"),
                model_llm=self.config.get("classifier_model_llm", "llm-sea3.1"),
                url_llm_api=self.config.get("classifier_url_llm_api", "http://**********:2033/api/llama3"),
                is_get_prompt_online=self.config.get("classifier_is_get_prompt_online", False)
            )
            logger.info("ClassificationQuestion engine initialized successfully for LLMChatProcessor.")
        except Exception as e:
            logger.critical(f"Failed to initialize ClassificationQuestion engine: {e}", exc_info=True)
            raise RuntimeError("Could not initialize Question Classifier") from e

        try:
            self.intent_verifier = IntentVerification(
                app_code=self.config.get("intent_verification_app_code", "TCB"),
                function_code=self.config.get("intent_verification_function_code", "tcb_intent_verify"),
                model_llm=self.config.get("intent_verification_model_llm", "llm-sea3.1"),
                url_llm_api=self.config.get("intent_verification_url_llm_api", "http://**********:2033/api/llama3"),
                is_get_prompt_online=self.config.get("intent_verification_is_get_prompt_online", False)
            )
            logger.info("IntentVerification engine initialized successfully for LLMChatProcessor.")
        except Exception as e:
            logger.critical(f"Failed to initialize IntentVerification engine: {e}", exc_info=True)
            raise RuntimeError("Could not initialize Intent Verifier") from e

        try:
            self.entity_verifier = EntityIntentVerification(
                app_code=self.config.get("intent_verification_app_code", "TCB"),
                function_code="tcb_entity_verify",
                model_llm=self.config.get("intent_verification_model_llm", "llm-sea3.1"),
                url_llm_api=self.config.get("intent_verification_url_llm_api", "http://**********:2033/api/llama3"),
                is_get_prompt_online=self.config.get("intent_verification_is_get_prompt_online", False)
            )
            logger.info("EntityVerification engine initialized successfully for LLMChatProcessor.")
        except Exception as e:
            logger.critical(f"Failed to initialize EntityVerification engine: {e}", exc_info=True)
            raise RuntimeError("Could not initialize Entity Verifier") from e

        self.user_message_history: Dict[str, set] = {}
        self.user_last_message_time: Dict[str, float] = {}
        self.message_cooldown = 5.0

    def _clean_text(self, text: str) -> str:
        return text.strip()

    async def process_message(self, record: Dict[str, Any], is_web_request: bool = False) -> Dict[str, Any]:
        message_text: str = self._clean_text(record.get("text", ""))
        
        if not message_text:
            logger.info("Empty message_text after cleaning.")
            return {"status": 0, "message": "empty_message", "action": {}}

        user_id: str = record.get("user_ID", record.get("conversation_id", "default_user"))
        current_time = time.time()

        # --- Duplicate Message Check ---
        normalized_text = message_text.lower()
        if user_id not in self.user_message_history:
            self.user_message_history[user_id] = set()
            self.user_last_message_time[user_id] = 0.0

        if normalized_text in self.user_message_history[user_id]:
            time_since_last_message = current_time - self.user_last_message_time.get(user_id, 0)
            if time_since_last_message < self.message_cooldown:
                logger.info(f"Message cooldown active for user '{user_id}': '{message_text}'")
                cooldown_response = deepcopy(record)
                cooldown_response["actions"] = ""
                cooldown_response["buttons"] = []
                cooldown_response["message"] = "cooldown"
                cooldown_response["error_code"] = 2
                cooldown_response["type"] = "text"
                if not is_web_request:
                    return {"status": 0, "message": "cooldown", "action": cooldown_response}
                return cooldown_response
        self.user_last_message_time[user_id] = current_time
        self.user_message_history[user_id].add(normalized_text)

        logger.info(f"Processing message for user '{user_id}': '{message_text}'")

        if self.config.get("debug_write_record_json", False):
            try:
                with open(self.config["record_json_path"], 'w', encoding='utf-8') as f_json:
                    json.dump(record, f_json, ensure_ascii=False, indent=2)
            except Exception as e_json:
                logger.error(f"Error writing debug record.json: {e_json}")
        
        try:
            message_text_rewritten = await rewrite_question(question=message_text)
        except Exception as e:
            message_text_rewritten = message_text

        final_response = deepcopy(record)
        user_name = record.get("user_name", "[user]")

        if is_web_request and (record.get("attachments") == 1 or (isinstance(record.get("attachments"), list) and len(record.get("attachments", [])) > 0)):
            logger.info(f"Message for user '{user_id}' has attachments.")
            final_response["actions"] = "Cảm ơn bạn đã cung cấp hình ảnh/tài liệu. Chuyên viên sẽ sớm liên hệ."
            final_response["buttons"] = []
            final_response["message"] = "success_attachment_fallback"
            final_response["error_code"] = 0
            final_response["type"] = "text"
            return final_response

        # --- Kiểm tra các ký tự vô nghĩa ---
        meaningless_patterns = [
            r"^\s*[.]+\s*$",
            r"^\s*[/]+\s*$",
            r"^\s*[?]+\s*$",
            r"^\s*$",
        ]
        if any(re.match(pat, message_text) for pat in meaningless_patterns):
            logger.info(f"Detected meaningless/gibberish input for user '{user_id}': '{message_text}' -> fallback response.")
            final_response = deepcopy(record)
            final_response["actions"] = DEFAULT_FALLBACK_MESSAGE
            final_response["buttons"] = []
            final_response["message"] = "nlu_fallback"
            final_response["error_code"] = 0
            final_response["type"] = "text"
            final_response['intent'] = {'name': 'nlu_fallback', 'confidence': 0.0}
            final_response['entities'] = []
            final_response['intent_ranking'] = [{'name': 'nlu_fallback', 'confidence': 0.0}]
            final_response['nlu_metadata'] = {}
            if not is_web_request:
                return {"status": 1, "message": "nlu_fallback", "action": final_response}
            return final_response

        # --- Phân loại câu hỏi bằng LLM ---
        nlu_result: Dict[str, Any]
        try:
            nlu_result = await asyncio.to_thread(
                self.question_classifier.classify_question_with_llm, 
                message_text_rewritten
            )
            logger.debug(f"NLU classification result: {nlu_result}")
        except Exception as e:
            logger.error(f"Error during NLU classification: {e}", exc_info=True)
            nlu_result = self.question_classifier._create_fallback_response(message_text_rewritten, time.time())

        # Ensure nlu_metadata exists
        if 'nlu_metadata' not in nlu_result:
            nlu_result['nlu_metadata'] = {}

        intent_name = nlu_result.get("intent", {}).get("name", "nlu_fallback")

        # --- Entity Verification ---
        entity_verification_response = self.entity_verifier.verify_intent_relevance(message_text_rewritten)
        is_entity_related = entity_verification_response.get("is_directly_related", False)
        logger.info(f"Entity verification result: {is_entity_related}")
        if not is_entity_related:
            final_response["actions"] = DEFAULT_FALLBACK_MESSAGE
            final_response["buttons"] = []
            final_response["message"] = "entity_verify_fallback"
            final_response["error_code"] = 0
            final_response["type"] = "text"
            final_response['intent'] = {'name': 'entity_verify_fallback', 'confidence': 0.0}
            final_response['entities'] = []
            final_response['intent_ranking'] = [{'name': 'entity_verify_fallback', 'confidence': 0.0}]
            final_response['nlu_metadata'] = {"entity_verify_status": entity_verification_response.get('status', 'unknown_error')}
            if not is_web_request:
                return {"status": 1, "message": "entity_verify_fallback", "action": final_response}
            return final_response

        # --- Intent Verification ---
        is_verified_and_related = False
        if intent_name != "nlu_fallback":
            try:
                verification_response = self.intent_verifier.verify_intent_relevance(message_text_rewritten, intent_name)
                is_verified_and_related = verification_response.get("is_directly_related", False)
                logger.info(f"Intent '{intent_name}' verification result: {is_verified_and_related}")
                nlu_result['nlu_metadata']['double_check_status'] = verification_response.get('status', 'unknown_error')
            except Exception as e:
                logger.error(f"Error during intent verification: {e}", exc_info=True)
                is_verified_and_related = False
                nlu_result['nlu_metadata']['double_check_status'] = f"verification_error: {str(e)}"
        else:
            nlu_result['nlu_metadata']['double_check_status'] = "nlu_fallback"
            
        response_objects: List[Dict[str, Any]]
        if is_verified_and_related:
            response_objects = classify_response(intent_name)
        else:
            # If not verified or fallback, use the specific fallback message
            response_objects = [{
                "text": "Cảm ơn bạn đã cung cấp thông tin. Sẽ có chuyên viên tư vấn phản hồi bạn sớm ạ."
            }]
            logger.info(f"Using fallback message due to intent verification failure or nlu_fallback for intent: {intent_name}")
            # Update NLU result to reflect the fallback (optional, for logging/debug)
            nlu_result['intent'] = {'name': 'intent_double_check_fallback', 'confidence': 1.0}
            nlu_result['intent_ranking'] = [{'name': 'intent_double_check_fallback', 'confidence': 1.0}]

        
        actions_text_parts = []
        buttons_payload_parts = []

        if not response_objects:
            logger.warning(f"classify_response returned empty for intent '{intent_name}'. Using default fallback.")
            actions_text_parts.append(DEFAULT_FALLBACK_MESSAGE)
        else:
            for res_obj in response_objects:
                if "text" in res_obj:
                    actions_text_parts.append(res_obj["text"])
                if "buttons" in res_obj:
                    buttons_payload_parts.extend(res_obj["buttons"])
        
        final_actions_text = "\n".join(filter(None, actions_text_parts)).strip()
        if not final_actions_text:
             final_actions_text = DEFAULT_FALLBACK_MESSAGE
             logger.warning(f"No text found in response_objects for intent '{intent_name}', using default fallback.")


        final_response["actions"] = final_actions_text.replace("[user_name]", user_name)
        final_response["buttons"] = convert_buttons_from_payload(buttons_payload_parts, platform=record.get("platform", ""))
        final_response["message"] = "success"
        final_response["error_code"] = 0
        final_response["type"] = "buttons" if final_response["buttons"] else "text"
        
        final_response['intent'] = nlu_result.get('intent')
        final_response['entities'] = nlu_result.get('entities')
        final_response['intent_ranking'] = nlu_result.get('intent_ranking')
        final_response['nlu_metadata'] = nlu_result.get('metadata')


        if not is_web_request:
            return {"status": 1, "message": "success", "action": final_response}

        logger.debug(f"Final response for user '{user_id}': {json.dumps(final_response, ensure_ascii=False, indent=2)}")
        return final_response

async def main_test_runner(config_override: Optional[Dict[str, Any]] = None):
    logger.info("Starting LLMChatProcessor test run with integrated Question Classifier...")
    try:
        processor = LLMChatProcessor(config=config_override)

        test_cases = [
            {
                "text": "Tôi muốn mở tk mới online",
                "user_ID": "test_user_open_acc_online",
                "platform": "web",
                "user_name": "Khách Mở TK Online"
            }
        ]

        for i, test_case_data in enumerate(test_cases):
            logger.info(f"\n--- Test Case {i+1}: {test_case_data['text']} ---")
            is_web = test_case_data.get("platform") == "web"
            reply_result = await processor.process_message(test_case_data, is_web_request=is_web)
            logger.info(f"Reply Result: {json.dumps(reply_result, ensure_ascii=False, indent=2)}")
            if i == 0:
                logger.info(f"--- Test Cooldown for: {test_case_data['text']} ---")
                cooldown_reply = await processor.process_message(test_case_data, is_web_request=is_web)
                logger.info(f"Cooldown Reply Result: {json.dumps(cooldown_reply, ensure_ascii=False, indent=2)}")


    except Exception as e:
        logger.error(f"Test runner failed: {e}", exc_info=True)
    logger.info("LLMChatProcessor test run finished.")

if __name__ == '__main__':
    asyncio.run(main_test_runner())