from icllmlib import LLM
from typing import Dict, List, Any
import json
import logging
import time
import os
from fastapi import HTTPException
from .intents_dict import intents_dict

# Logger Setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

class ClassificationQuestion:
    def __init__(
            self,
            app_code: str = "TCB",
            function_code: str = "tcb_classify",
            model_llm: str = "llm-sea3.1",
            url_prompt: str = "https://staging.pontusinc.com/api/chatbot/v1/prompt/list",
            llm_name: str = "basic_nlp_classify",
            url_llm_api: str = "http://10.9.3.241:2033/api/llama3",
            base_dir_log: str = "logs/llm_logs",
            base_dir_post_process: str = "logs/llm_logs/post_process",
            base_dir_prompt: str = "logs/llm_logs/prompt",
            is_get_prompt_online: bool = False,
            time_sleep: int = 5,
            is_log: bool = True,
    ):
        # Create necessary directories
        os.makedirs(base_dir_log, exist_ok=True)
        os.makedirs(base_dir_post_process, exist_ok=True)
        os.makedirs(base_dir_prompt, exist_ok=True)
        
        try:
            self.llm_engine = LLM(
                app_code=app_code,
                function_code=function_code,
                model_llm=model_llm,
                url_prompt=url_prompt,
                llm_name=llm_name,
                url_llm_api=url_llm_api,
                url_get_llm_api="",
                base_dir_log=base_dir_log,
                base_dir_post_process=base_dir_post_process,
                base_dir_prompt=base_dir_prompt,
                is_log=True,
                is_show_console=False,
                is_get_prompt_online=is_get_prompt_online,
            )
            logger.info("LLM engine initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize LLM engine: {e}", exc_info=True)
            raise

        if is_get_prompt_online:
            try:
                self.llm_engine.get_prompt_frequency(time_sleep=time_sleep)
                logger.info("Successfully got prompt frequency")
            except Exception as e:
                logger.error(f"Failed to get prompt frequency: {e}", exc_info=True)

    def classify_question_with_llm(self, question: str) -> Dict[str, Any]:
        """Classify a question using LLM and return formatted response."""
        start_time = time.time()
        
        if not question or question.strip() == "":
            logger.warning("Empty question received")
            return self._create_fallback_response(question, start_time)

        try:
            prompt_data = {"$$question": question}
            llm_start_time = time.time()
            
            response = self.llm_engine.generate(
                prompt_data, 
                temperature=0.1, 
                max_decoding_length=256,
                repetition_penalty=1.1
            )
            
            llm_response_time = time.time() - llm_start_time
            logger.info(f"LLM response time: {llm_response_time:.2f}s")
            
            if response and response[0].get('answer_norm'):
                intent_name = response[0]['answer_norm'].lower()
                confidence = 1.0
                logger.info(f"LLM classified intent as: {intent_name}")
            else:
                intent_name = "nlu_fallback"
                confidence = 0.0
                logger.warning(f"LLM did not return valid intent for: {question}")

            result = {
                "intent": {
                    "name": intent_name,
                    "confidence": confidence
                },
                "intent_ranking": [
                    {
                        "name": intent_name,
                        "confidence": confidence
                    }
                ],
                "entities": [],
                "text": question,
                "message_id": None,
                "metadata": {
                    "processing_time": time.time() - start_time,
                    "llm_response_time": llm_response_time
                }
            }
            
            logger.debug(f"Classification result: {json.dumps(result, ensure_ascii=False)}")
            return result

        except Exception as e:
            logger.error(f"Error during question classification: {e}", exc_info=True)
            return self._create_fallback_response(question, start_time)

    def _create_fallback_response(self, question: str, start_time: float) -> Dict[str, Any]:
        """Create a fallback response when classification fails."""
        return {
            "intent": {"name": "nlu_fallback", "confidence": 0.0},
            "intent_ranking": [{"name": "nlu_fallback", "confidence": 0.0}],
            "entities": [],
            "text": question,
            "message_id": None,
            "metadata": {
                "processing_time": time.time() - start_time,
                "llm_response_time": 0
            }
        }

def classify_response(intent_name: str) -> List[str]:
    """Get response text for a given intent."""
    try:
        intent_name = intent_name.lower()
        response_key = f"utter_{intent_name}"
        
        if response_key in intents_dict["responses"]:
            responses = intents_dict["responses"][response_key]
            logger.info(f"Found {len(responses)} responses for intent: {intent_name}")
            return responses
        
        logger.warning(f"No responses found for intent: {intent_name}")
        return ["Xin lỗi, tôi chưa hiểu ý bạn. Bạn có thể nói rõ hơn được không?"]
    except Exception as e:
        logger.error(f"Error getting response for intent {intent_name}: {e}", exc_info=True)
        return ["Xin lỗi, tôi chưa hiểu ý bạn. Bạn có thể nói rõ hơn được không?"]

if __name__ == "__main__":
    try:
        classifier = ClassificationQuestion()
        test_question = "phải làm gì để khắc phục ạ?"
        
        result = classifier.classify_question_with_llm(test_question)
        print("\nClassification Result:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        if result["intent"]["name"] != "nlu_fallback":
            responses = classify_response(result["intent"]["name"])
            print(f"\nResponses for intent '{result['intent']['name']}':")
            print(json.dumps(responses, indent=2, ensure_ascii=False))
    except Exception as e:
        logger.error(f"Test failed: {e}", exc_info=True)

