import os
os.environ["CUDA_VISIBLE_DEVICES"] = "-1"

import threading
import re
import json
import asyncio
import logging
import time
from copy import deepcopy
from typing import Dict, Any, Optional, List, Tuple
from rasa.core.http_interpreter import RasaNLUHttpInterpreter
from rasa.core.agent import Agent
from rasa.core.utils import EndpointConfig
from .buttons import convert_buttons
from .rewrite_ques import rewrite_question

extra_intents = []
host = "**********"

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
MODEL_FILENAME = '20250528-154633-coffee-literal.tar.gz'
model_path = os.path.join(SCRIPT_DIR, '..', 'models', MODEL_FILENAME)
model_path = os.path.normpath(model_path)

INTENT_NAMES_FILENAME = 'intent_names.txt'
intent_names_path = os.path.join(SCRIPT_DIR, '..', 'data', INTENT_NAMES_FILENAME)
intent_names_path = os.path.normpath(intent_names_path)

description_intent = {}
if os.path.exists(intent_names_path):
    with open(intent_names_path, encoding="utf-8") as f:
        for line in f:
            if line.strip():
                parts = line.split(":", 1)
                if len(parts) == 2:
                    description_intent[parts[0].strip()] = parts[1].strip()
                else:
                    print(f"Warning: Malformed line in {intent_names_path}: {line.strip()}")
else:
    print(f"Warning: Intent names file not found at {intent_names_path}. 'description_intent' will be partially initialized.")

description_intent["nlu_fallback"] = description_intent.get("nlu_fallback", "Không hiểu")

# --- Logger Setup ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# --- Configuration ---
DEFAULT_CONFIG: Dict[str, Any] = {
    "rasa_model_filename": "20250528-154633-coffee-literal.tar.gz",
    "models_dir": os.path.join(SCRIPT_DIR, "..", "models"),
    "intent_names_filename": "intent_names.txt",
    "data_dir": os.path.join(SCRIPT_DIR, "..", "data"),
    "rasa_nlu_host": "0.0.0.0",
    "rasa_nlu_port": 5005,
    "rasa_action_host": "0.0.0.0",
    "rasa_action_port": 5055,
    "extra_intents_to_ignore_in_disambiguation": [],
    "min_confidence_for_direct_action": 0.75,
    "min_confidence_for_suggestion": 0.2,
    "debug_write_record_json": True,
    "record_json_path": os.path.join(SCRIPT_DIR, "debug_record.json")
}

class RasaChatProcessor:
    """
    Handles loading Rasa agent, intent descriptions, and processing chat messages.
    """
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = deepcopy(DEFAULT_CONFIG)
        if config:
            self.config.update(config)
        self.model_path: str = os.path.normpath(
            os.path.join(self.config["models_dir"], self.config["rasa_model_filename"])
        )
        self.intent_names_path: str = os.path.normpath(
            os.path.join(self.config["data_dir"], self.config["intent_names_filename"])
        )
        self.description_intent: Dict[str, str] = self._load_intent_descriptions()
        self.agent: Optional[Agent] = self._load_agent()
        if not self.agent:
            logger.critical("Rasa agent could not be loaded. Chat processor will not function.")
        self.user_message_history: Dict[str, set] = {}
        self.user_last_message_time: Dict[str, float] = {}
        self.message_cooldown = 5.0

    def _load_intent_descriptions(self) -> Dict[str, str]:
        """Loads intent descriptions from the configured file."""
        descriptions: Dict[str, str] = {}
        if not os.path.exists(self.intent_names_path):
            logger.warning(f"Intent names file not found at: {self.intent_names_path}")
        else:
            try:
                with open(self.intent_names_path, "r", encoding="utf-8") as f:
                    for line in f:
                        if line.strip():
                            parts = line.split(":", 1)
                            if len(parts) == 2:
                                descriptions[parts[0].strip()] = parts[1].strip()
                            else:
                                logger.warning(f"Malformed line in {self.intent_names_path}: {line.strip()}")
            except Exception as e:
                logger.error(f"Error reading intent names file {self.intent_names_path}: {e}")
        
        descriptions.setdefault("nlu_fallback", "Cảm ơn bạn đã cung cấp thông tin. Sẽ có chuyên viên tư vấn cho bạn sớm ạ.")
        logger.info(f"Loaded {len(descriptions)} intent descriptions.")
        return descriptions

    def _load_agent(self) -> Optional[Agent]:
        """Loads the Rasa agent from the configured model path."""
        if not os.path.exists(self.model_path):
            logger.error(f"Rasa model file not found at: {self.model_path}")
            return None
        
        try:
            action_endpoint_url = f"http://{self.config['rasa_action_host']}:{self.config['rasa_action_port']}/webhook"
            action_endpoint = EndpointConfig(action_endpoint_url)
            
            agent = Agent.load(self.model_path, action_endpoint=action_endpoint)
            logger.info(f"Rasa agent loaded successfully from {self.model_path}.")
            return agent
        except Exception as e:
            logger.error(f"Error loading Rasa agent from {self.model_path}: {e}", exc_info=True)
            return None

    def _clean_text(self, text: str) -> str:
        """Basic text cleaning (placeholder)."""
        return text

    async def process_message(self, record: Dict[str, Any], is_web_request: bool = False) -> Dict[str, Any]:
        message_text: str = self._clean_text(record.get("text", ""))
        if not message_text.strip():
            logger.info("Empty message_text received. Skipping response generation.")
            return {"status": 0, "message": "empty_message", "action": {}}

        user_id: str = record.get("user_ID", record.get("conversation_id", "default_user"))
        current_time = time.time()

        # --- Duplicate Message Check ---
        normalized_text = message_text.strip().lower()
        if user_id not in self.user_message_history:
            self.user_message_history[user_id] = set()
            self.user_last_message_time[user_id] = 0.0

        # Kiểm tra cooldown cho message giống nhau
        if normalized_text in self.user_message_history:
            time_since_last_message = current_time - self.user_last_message_time.get(user_id, 0)
            if time_since_last_message < self.message_cooldown:
                logger.info(f"Message cooldown active for user '{user_id}': '{message_text}' (cooldown: {self.message_cooldown}s)")
                cooldown_response = deepcopy(record)
                cooldown_response["actions"] = ""
                cooldown_response["buttons"] = []
                cooldown_response["message"] = "cooldown"
                cooldown_response["error_code"] = 2
                cooldown_response["type"] = "text"
                if not is_web_request:
                    return {"status": 0, "message": "cooldown", "action": cooldown_response}
                return cooldown_response

        # Cập nhật thời gian xử lý message
        self.user_last_message_time[user_id] = current_time
        self.user_message_history[user_id].add(normalized_text)

        logger.info(f"Processing message for user '{user_id}': '{message_text}'")

        if self.config.get("debug_write_record_json", False):
            try:
                with open(self.config["record_json_path"], 'w', encoding='utf-8') as f_json:
                    json.dump(record, f_json, ensure_ascii=False, indent=2)
                logger.debug(f"Debug record saved to {self.config['record_json_path']}")
            except Exception as e_json:
                logger.error(f"Error writing debug record.json: {e_json}")

        # --- Rewrite question ---
        print("Đầu vào: ", message_text)
        try:
            message_text = await rewrite_question(question=message_text)
        except Exception as e:
            logger.warning(f"Error rewriting question: {e}")
        # --- NLU Parsing ---
        parsed_data = await self.agent.parse_message(message_text)
        print("Đầu ra: ", message_text)
        logger.debug(f"NLU parse result: {parsed_data}")

        # --- Initialize Response ---
        final_response = deepcopy(record)
        final_response['intent'] = parsed_data.get('intent')
        final_response['entities'] = parsed_data.get('entities')
        final_response['intent_ranking'] = parsed_data.get('intent_ranking', [])
        user_name = record.get("user_name", "[user]")

        # --- Handle Attachments (specific to web/richer clients) ---
        if is_web_request:
            attachments = record.get("attachments", 0)
            if attachments == 1 or (isinstance(attachments, list) and len(attachments) > 0):
                logger.info(f"Message for user '{user_id}' has attachments.")
                final_response["actions"] = "Cảm ơn bạn đã cung cấp thông tin. Sẽ có chuyên viên tư vấn cho bạn sớm ạ."
                final_response["buttons"] = []
                final_response["message"] = "success"
                final_response["error_code"] = 0
                final_response["type"] = "text"
                return final_response

        # --- Core Logic: Direct Action or Fallback ---
        actions_text_parts: List[str] = []
        buttons_list: List[Dict[str, Any]] = []

        top_intent_data = parsed_data.get("intent", {})
        top_intent_name = top_intent_data.get("name")
        top_intent_confidence = top_intent_data.get("confidence", 0.0)

        if top_intent_confidence < self.config["min_confidence_for_direct_action"]:
            logger.info(f"Intent '{top_intent_name}' has confidence {top_intent_confidence:.2f} < {self.config['min_confidence_for_direct_action']}. Using NLU fallback message.")
            actions_text_parts.append(self.description_intent.get("nlu_fallback", "Cảm ơn bạn đã cung cấp thông tin. Sẽ có chuyên viên tư vấn cho bạn sớm ạ."))
        else:
            logger.info(f"Confident intent '{top_intent_name}' (confidence: {top_intent_confidence:.2f} >= {self.config['min_confidence_for_direct_action']}). Passing '{message_text}' to Rasa Core.")
            rasa_actions = await self.agent.handle_text(message_text, sender_id=user_id)
            if rasa_actions:
                for action_response in rasa_actions:
                    if action_response.get("text"):
                        actions_text_parts.append(action_response.get("text"))
                    if action_response.get("buttons"):
                        buttons_list.extend(action_response.get("buttons"))

            if not actions_text_parts and not buttons_list:
                logger.warning(f"Rasa Core returned no text/buttons for confident intent '{top_intent_name}'. Using its description or general fallback.")
                actions_text_parts.append(
                    self.description_intent.get(
                        top_intent_name,
                        self.description_intent.get("nlu_fallback", "Cảm ơn bạn đã cung cấp thông tin. Sẽ có chuyên viên tư vấn cho bạn sớm ạ.")
                    )
                )

        actions_text = "\n".join(filter(None, actions_text_parts)).strip()

        # --- Finalize Response Structure ---
        final_response["actions"] = actions_text.replace("[user_name]", user_name)
        final_response["buttons"] = convert_buttons(buttons_list, platform=record.get("platform", ""))
        final_response["message"] = "success"
        final_response["error_code"] = 0
        final_response["type"] = "buttons" if final_response["buttons"] else "text"

        # Update intent ranking names for display/logging
        for intent_info in final_response.get("intent_ranking", []):
            intent_info["name"] = self.description_intent.get(intent_info["name"], intent_info["name"])

        if not is_web_request:
            return {"status": 1, "message": "success", "action": final_response}

        logger.debug(f"Final response for user '{user_id}': {final_response}")
        return final_response

async def main_test_runner(config_override: Optional[Dict[str, Any]] = None):
    """
    Main runner for testing the RasaChatProcessor.
    """
    processor = RasaChatProcessor(config=config_override)
    if not processor.agent:
        logger.error("Exiting due to agent loading failure.")
        return


    test_record_web_no_attach = {
        "text": "Tôi muốn mở tài khoản ngân hàng.",
        "user_ID": "web_user_456",
        "conversation_id": "conv_web_789",
        "attachments": 0,
        "platform": "web",
        "user_name": "Khách Web"
    }
    logger.info(f"\n--- Testing web reply (no attachments) for: {test_record_web_no_attach['text']} ---")
    web_reply_result = await processor.process_message(test_record_web_no_attach, is_web_request=True)
    logger.info(f"Web Reply Result (no attach): {json.dumps(web_reply_result, ensure_ascii=False, indent=2)}")



if __name__ == '__main__':
    if not os.path.exists(os.path.join(DEFAULT_CONFIG["models_dir"], DEFAULT_CONFIG["rasa_model_filename"])) :
        logger.critical(f"CRITICAL: Model file not found at {os.path.join(DEFAULT_CONFIG['models_dir'], DEFAULT_CONFIG['rasa_model_filename'])}. Please check the path.")
    elif not os.path.exists(os.path.join(DEFAULT_CONFIG["data_dir"], DEFAULT_CONFIG["intent_names_filename"])):
         logger.critical(f"CRITICAL: Intent names file not found at {os.path.join(DEFAULT_CONFIG['data_dir'], DEFAULT_CONFIG['intent_names_filename'])}.")
    else:
        logger.info("Starting RasaChatProcessor test run...")
        asyncio.run(main_test_runner())
        logger.info("RasaChatProcessor test run finished.")