import json
import requests
import time

URL_COMMON_CONFIG = "https://staging.pontusinc.com/api/chatbot/v1/common-config/list"
URL_OPEN_DOMAIN_DETAIL = "https://staging.pontusinc.com/api/chatbot/v1/open-domain-config/{}/detail"

class CommonConfigClient():
    def __init__(self, path = "/hdd/thienthan/chatbot-rasa/database/data/common_config_2.json"):
        self.path = path
    # def get_common_config(self):
    #     try:
    #         with open(self.path) as f:
    #             config = json.load(f)
    #     except:
    #         config = {}
        
    #     return config

    def get_open_domain_detail(self , id, url, retries = 3):
        if id == "semantic-tcb": id = "e7071e16-3604-4f9c-a5b9-2d6a49a72f66"
        if id == "semantic-tcb-1.1": id = "e708040a-0f00-49cb-9b80-aff50c7f4cfe"
        result = {}

        error = True
        while retries > 0 and error:
            try:
                res = requests.get(url.format(id)).json()
                result = res["result"]
                error = False
            except Exception as ex:
                print(f"[ERROR] get_open_domain_detail: {str(ex)}")
                time.sleep(2)
            retries -= 1
        return result

    def get_common_config(self, customer_id, url = URL_COMMON_CONFIG, retries = 3):
        json_body = {
            "filters": {
                "filter_customer_id": {
                "operator": {
                    "operator": 8
                },
                "field": "customer_id",
                "value": customer_id
                }
            }
        }
        config = {}
        error = True
        while retries > 0 and error:
            try:
                res = requests.post(url, json=json_body)
                res = res.json()
                result = res["result"]["data"][0]
                config = {}
                config["api_connect"] = result.get("api_connect", "")
                config.update(json.loads(result["config"]))
                
                error = False
                # print(config)
            except Exception as ex:
                print(ex)
                time.sleep(2)
            retries -= 1
                
        
        return config

    def get_common_config_full(self, customer_id, url = URL_COMMON_CONFIG):
        config = self.get_common_config(customer_id=customer_id, url=url)
        retrieval_model_id = config.get("retrieval_model", "").strip()

        open_domain_cfg = {}
        if retrieval_model_id:
            open_domain_cfg = self.get_open_domain_detail(id=retrieval_model_id, url = URL_OPEN_DOMAIN_DETAIL)
        url_opd = open_domain_cfg.get("url", "")
        url_opd_split = url_opd.split("$$")
        config["url_retrieve"] = url_opd_split[0].strip()
        config["url_embed"] = url_opd_split[1].strip() if len(url_opd_split) > 1 else ""
        return config

    def save_config(self, config):
        with open(self.path, 'w') as f:
            json.dump(config, f, ensure_ascii=False)

    def update_config(self, data):
        config = self.get_common_config()
        config.update(data)
        self.save_config(config)
        return config

if __name__ == '__main__':
    customer_id = "e7071e16-3a27-475c-915a-335fdb52d07b" 
    customer_id = "e7080509-3315-4007-b006-cfd5a5bb6e66" # tcb full 2.1
    a = CommonConfigClient()
    b = a.get_common_config_full(customer_id)
    print(b)