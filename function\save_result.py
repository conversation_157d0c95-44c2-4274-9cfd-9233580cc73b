import requests
import pandas as pd
import os
import datetime

API_SAVE_RESULT_CHATBOT = "http://10.9.3.70:32620/api/socialcare/ChatbotWebhook/receiver"
EXCEL_LOG_FILE = "hybrid_chatbot_results.xlsx"

def save_result_chatbot(result):
    json_body = result
    res = requests.post(API_SAVE_RESULT_CHATBOT, json = result)
    print(res.text)

def save_hybrid_results_to_excel(received_data, rasa_result, llm_result, final_result):
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
    
    # Extract data from received_data
    user_id = received_data.get('user_ID', received_data.get('conversation_id', 'default_user'))
    message_content = received_data.get('text', '')

    # Extract data from Rasa result
    rasa_intent_name = rasa_result.get('intent', {}).get('name', 'N/A')
    rasa_intent_confidence = rasa_result.get('intent', {}).get('confidence', 0.0)
    rasa_response_text = rasa_result.get('actions', 'N/A') # Assuming 'actions' key holds the response text
    if isinstance(rasa_response_text, dict): # In case 'actions' is a dict, get 'text' from it
        rasa_response_text = rasa_response_text.get('text', 'N/A')
    
    # Extract data from LLM result
    llm_intent_name = llm_result.get('intent', {}).get('name', 'N/A')
    llm_intent_confidence = llm_result.get('intent', {}).get('confidence', 0.0)
    llm_response_text = llm_result.get('actions', 'N/A') # Assuming 'actions' key holds the response text
    
    # Determine which model's output was selected as final
    selected_model = "LLM"
    if rasa_result == final_result: # This comparison might not be robust due to deepcopy
        selected_model = "Rasa"
    
    # A more robust way to check if Rasa's output was selected (based on confidence logic in consumer)
    rasa_top_confidence = rasa_result.get('intent', {}).get('confidence', 0.0)
    rasa_top_intent = rasa_result.get('intent', {}).get('name')
    # Using a placeholder for min_confidence_for_direct_action, as it's defined in RasaChatProcessor
    # For now, let's just assume the final_result directly indicates the chosen one.
    # In a real scenario, you'd pass the `selected_model` flag from `consumer_chatbot_hybrid.py`

    final_response_text = final_result.get('actions', 'N/A')

    new_row = {
        'Timestamp': timestamp,
        'User ID': user_id,
        'Message Content': message_content,
        'Rasa Intent Name': rasa_intent_name,
        'Rasa Intent Confidence': rasa_intent_confidence,
        'Rasa Response': rasa_response_text,
        'LLM Intent Name': llm_intent_name,
        'LLM Intent Confidence': llm_intent_confidence,
        'LLM Response': llm_response_text,
        'Selected Model': selected_model,
        'Final Response Text': final_response_text
    }

    df = pd.DataFrame([new_row])

    if os.path.exists(EXCEL_LOG_FILE):
        try:
            # Read existing data and append
            existing_df = pd.read_excel(EXCEL_LOG_FILE)
            df = pd.concat([existing_df, df], ignore_index=True)
        except Exception as e:
            print(f"Error reading existing Excel file, creating new one: {e}")
            # If error, simply proceed with new df
    
    # Save to Excel
    try:
        df.to_excel(EXCEL_LOG_FILE, index=False)
        print(f"Successfully logged results to {EXCEL_LOG_FILE}")
    except Exception as e:
        print(f"Failed to write to Excel file: {e}")