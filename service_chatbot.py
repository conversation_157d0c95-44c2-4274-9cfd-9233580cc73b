import json
import time
import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

from function.process_messages import RasaChatProcessor, DEFAULT_CONFIG
from rabbit_common import ICRabbitMQ, init_rabbit_queue, queue_chatbot_message_params

# --- Logger Setup ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

app = FastAPI()

# --- Initialize Rasa Chat Processor ---
chat_processor = RasaChatProcessor(config=DEFAULT_CONFIG)

if not chat_processor.agent:
    logger.critical("Rasa agent failed to load in RasaChatProcessor. FastAPI service might not function correctly for chatbot endpoints.")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class Input(BaseModel):
    text: str
    user_ID: str
    context: str = ""
    attachments: int = 0
    customer: str = "default_customer"
    chatbot_open_domain: list = []

class InputWeb(BaseModel):
    text: str
    user_ID: str
    attachments: int = 0

class InputProduct(BaseModel):
    text: str
    conversation_id: str
    page_id: str
    attachments: list = []
    history: list = []
    platform: str
    fb_scope_id: str
    user_name: str = "user_name"
    is_first_message: bool = False
    is_duplicate_message: bool = False

@app.post("/api/v1/chatbot/tcb")
async def chat_tcb(item: Input):
    logger.info(f"Received request for /api/v1/chatbot/tcb: user_ID={item.user_ID}, text='{item.text}'")
    output: dict
    try:
        input_data = {
            "text": item.text,
            "user_ID": item.user_ID,
            "context": item.context,
            "attachments": item.attachments,
            "customer": item.customer,
            "chatbot_open_domain": item.chatbot_open_domain
        }
        output = await chat_processor.process_message(input_data, is_web_request=False)
        logger.debug(f"Response from chat_processor for /tcb: {output}")
    except Exception as ex:
        logger.error(f"Error in /api/v1/chatbot/tcb: {ex}", exc_info=True)
        output = {
            "status": -1,
            "message": f"error: {ex}",
            "action": output.get("action") if "output" in locals() and isinstance(output, dict) else None
        }
    return output

@app.post("/chatbot")
async def chat_tcb_web(item: InputWeb):
    logger.info(f"Received request for /chatbot: user_ID={item.user_ID}, text='{item.text}', attachments={item.attachments}")
    result: dict
    try:
        input_data = {
            "text": item.text,
            "user_ID": item.user_ID,
            "attachments": item.attachments
        }
        result = await chat_processor.process_message(input_data, is_web_request=True)
        logger.debug(f"Response from chat_processor for /chatbot: {result}")
    except Exception as ex:
        logger.error(f"Error in /chatbot: {ex}", exc_info=True)
        raise ex
    return result

@app.post("/api/v1/chatbot")
async def chat_product(item: InputProduct):
    logger.info(f"Received request for /api/v1/chatbot: conversation_id={item.conversation_id}, text='{item.text}'")
    result: dict
    try:
        result = await chat_processor.process_message(item.__dict__, is_web_request=True)
        logger.debug(f"Response from chat_processor for /api/v1/chatbot: {result}")
    except Exception as ex:
        logger.error(f"Error in /api/v1/chatbot: {ex}", exc_info=True)
        raise ex
    return result

@app.post("/api/v1/chatbot_async")
async def chat_product_async(item: InputProduct):
    logger.info(f"Received request for /api/v1/chatbot_async: conversation_id={item.conversation_id}, text='{item.text}'")
    status = 1
    message = "success"

    params = queue_chatbot_message_params
    usr_name = params["UserName"]
    password = str(params["Password"])
    host = params["HostName"]
    virtual_host = params["VirtualHost"]
    queue_name = params["Queue"]

    data_push = item.__dict__
    data_push["time"] = time.time()

    logger.debug(f"Preparing to push to RabbitMQ: {data_push}")

    try:
        channel_consumer, rb_consumer, queue_consumer = init_rabbit_queue(usr_name, password, host, virtual_host, queue_name, True, 10)
        
        ICRabbitMQ.publish_message(channel_consumer,  queue_consumer, data_push, priority=1, delivery_mode=2, exchange='')
        logger.info(f"Message published to RabbitMQ queue: {queue_consumer}")

    except Exception as ex:
        logger.error(f"Error publishing to RabbitMQ in /api/v1/chatbot_async: {ex}", exc_info=True)
        message = f"error: {str(ex)}"
        status = -1
    
    result = {
        "message": message,
        "status": status
    }
    return result


if __name__ == '__main__':
    import uvicorn
    logger.info("Starting FastAPI application for Chatbot Service...")
    uvicorn.run(app, host="0.0.0.0", port=26031)