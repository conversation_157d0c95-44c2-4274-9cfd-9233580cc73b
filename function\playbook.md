# Playbook Vận hành Chatbot Techcombank (Phiên bản đơn giản)

Tài liệu này hướng dẫn các quy trình cơ bản để vận hành, bảo trì và phát triển chatbot.

## 1. Tổng quan

*   **<PERSON><PERSON><PERSON> tiêu:** Chatbot tự động trả lời các câu hỏi thường gặp (FAQ) của khách hàng Techcombank.
*   **Kiến trúc:** Hệ thống sử dụng kiến trúc song song (hybrid), kết hợp giữa **Rasa** và **Mô hình ngôn ngữ lớn (LLM)** để nhận diện ý định của người dùng, đảm bảo độ chính xác và linh hoạt.

## 2. Kiến trúc hệ thống

```mermaid
graph TD
    A[Tin nhắn người dùng] --> B(Tiền xử lý: Viết lại câu hỏi bằng LLM);
    B --> C{Phân luồng xử lý};
    C --> D[Luồng Rasa NLU];
    C --> E[Luồng LLM];
    D --> F[Phân loại Intent];
    E --> G[Phân loại & xác thực Intent (Verify)];
    F --> H(Lấy câu trả lời từ domain.yml);
    G --> H;
    H --> I[Gửi câu trả lời cho người dùng];
    H --> J[Lưu log kết quả];
```

*   **Tiền xử lý:** Câu hỏi của người dùng được LLM trong `rewrite_ques.py` viết lại để rõ ràng và chuẩn hóa hơn.
*   **Phân luồng:** Hệ thống có thể xử lý song song bằng cả Rasa và LLM.
*   **Luồng Rasa:** Sử dụng dữ liệu trong `nlu.yml` để huấn luyện và nhận diện intent.
*   **Luồng LLM:** Sử dụng sức mạnh của LLM để hiểu ngữ nghĩa câu hỏi và phân loại intent, có thêm các bước xác thực (verify) để tăng độ tin cậy.
*   **Lấy câu trả lời:** Dù được xử lý bằng luồng nào, câu trả lời cuối cùng vẫn được lấy từ file `domain.yml` dựa trên intent đã được xác định.

## 3. Các thành phần chính

*   `data/nlu.yml`: Chứa các câu hỏi mẫu (ví dụ) để huấn luyện cho bot hiểu người dùng.
*   `domain.yml`: Định nghĩa các ý định (intent) và chứa các câu trả lời (`responses`) của bot.
*   `data/intent_names.txt`: Ánh xạ tên intent kỹ thuật (vd: `intent_1`) sang mô tả dễ hiểu (vd: `hỏi mở thẻ tín dụng`). **Rất quan trọng!**
*   `models/`: Thư mục chứa các phiên bản mô hình đã được huấn luyện.
*   `function/`: Chứa mã nguồn xử lý logic chính của chatbot, bao gồm:
    *   `process_messages.py`: Xử lý luồng Rasa.
    *   `process_messages_llm*.py`: Các file xử lý luồng LLM với các bước kiểm tra khác nhau.
    *   `rewrite_ques.py`: Module tiền xử lý câu hỏi bằng LLM.
    *   `classify/*.py`: Các module cốt lõi cho việc phân loại và xác thực bằng LLM.
    *   `save_result.py`: Lưu log kết quả xử lý của cả hai luồng.

## 4. Quy trình phổ biến nhất: Thêm một cặp Hỏi-Đáp (FAQ) mới

Đây là tác vụ bạn sẽ thực hiện thường xuyên nhất.

**Ví dụ:** Thêm câu hỏi về "cách gia hạn thẻ".

### Bước 1: Xác định và đặt tên Intent

*   Phân tích câu hỏi để tìm ra ý định chính. Với "cách gia hạn thẻ", ý định là `gia_han_the`.
*   Đặt tên intent mới, ví dụ: `intent_gia_han_the`.
    *   **Lưu ý:** Nên đặt tên có ý nghĩa thay vì `intent_XX` để dễ quản lý.

### Bước 2: Cập nhật file `data/nlu.yml`

*   Thêm intent mới và các câu hỏi ví dụ mà người dùng có thể hỏi. Càng nhiều biến thể càng tốt.

```yaml
- intent: intent_gia_han_the
  examples: |
    - làm sao để gia hạn thẻ
    - thẻ của tôi sắp hết hạn, cần làm gì
    - thủ tục gia hạn thẻ tín dụng
    - gia hạn thẻ atm online được không
```

### Bước 3: Cập nhật file `data/intent_names.txt`

*   Thêm một dòng mới để mô tả cho intent vừa tạo. **Bước này bắt buộc phải có**, vì cả luồng Rasa và LLM đều sử dụng mô tả này.

```
intent_gia_han_the: Gia hạn thẻ
```

### Bước 4: Cập nhật file `domain.yml`

1.  **Thêm intent mới vào danh sách `intents`:**

    ```yaml
    intents:
      - intent_1
      # ... các intent khác
      - intent_gia_han_the # <--- Thêm vào đây
    ```

2.  **Thêm câu trả lời tương ứng trong mục `responses`:**
    Tên câu trả lời thường bắt đầu bằng `utter_` + tên intent.

    ```yaml
    responses:
      # ... các câu trả lời khác
      utter_intent_gia_han_the:
      - text: "Để gia hạn thẻ, Techcombank sẽ tự động gửi thẻ mới đến địa chỉ đã đăng ký của bạn trước ngày hết hạn. Bạn vui lòng đảm bảo thông tin liên lạc của mình là chính xác."
    ```

### Bước 5: Huấn luyện lại mô hình

*   Mở terminal (dòng lệnh) trong thư mục gốc của dự án.
*   Chạy lệnh sau:

```bash
rasa train
```

*   Lệnh này sẽ tạo một file model mới trong thư mục `models/`.

### Bước 6: Cập nhật và kiểm thử

*   Cập nhật tên model mới trong file cấu hình hoặc file xử lý chính (ví dụ: `function/process_messages.py`).
*   Khởi động lại chatbot và dùng các câu hỏi mẫu để kiểm tra xem bot đã trả lời đúng hay chưa.