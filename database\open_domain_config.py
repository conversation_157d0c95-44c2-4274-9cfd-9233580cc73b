from elasticsearch import Elasticsearch
from elasticsearch.helpers.actions import bulk
import requests
import json
import time

class OpenDomainConfigElasticsearch():
    def __init__(self, host = "http://**********:9200", timeout=30, max_retries=10, retry_on_timeout=True ):
        self.elas = Elasticsearch(hosts=host, timeout=timeout, max_retries=max_retries, retry_on_timeout=retry_on_timeout)
        self.config_index = "chatbot.config.open_domain"


    def create_index(self, index_name):
        if not self.elas.indices.exists(index=index_name):
            body = {
                "settings": {
                    "index": {
                        # "refresh_interval": "-1",
                        "number_of_shards": 1,   
                        "number_of_replicas": 0
                    }
                },
            }
            res = self.elas.indices.create(index=index_name,ignore=400, body=body)
            print(res)
        else:
            print(f'index {index_name} exist')

        # self.elas.create(index=index_name)
    
    def add_items(self, index_name, data):
        insert_data = []
        for d in data:
            d["last_updated_time"] = int(time.time())
            dt = {
                "_index": index_name,
                # "_id": id_elas,
                "_source": d
            }
            insert_data.append(dt)
        bulk(self.elas, insert_data, refresh='wait_for')
 
    def delete_items(self, index_name, ids):
        data = []
        for id_ in ids:
            d = {
                "_op_type": "delete",
                "_id": id_,
                "_index": index_name,
            }
            data.append(d)
        bulk(self.elas, data, refresh='wait_for')

    def update_items(self, items, index_name):
        insert_data = []
        for d in items:
            d["data"]["last_updated_time"] = int(time.time())
            dt = {
                "_index": index_name,
                "_id": d["id"],
                "_source": d["data"]
            }
            insert_data.append(dt)
        bulk(self.elas, insert_data, refresh='wait_for')

        # bulk_data_update = []
        # for item in items:
        #     scripts = []
        #     for key in item["data"]:
        #         script = f"ctx.{key} = {item['data'][key]}"
        #         scripts.append(script)
        #     script_txt = ";".join(scripts)
        #     print(script_txt)
        #     bulk_data_update.append({
        #         "_op_type": "update",
        #         "_id": item["id"],
        #         "_index": index_name,
        #         "script": {
        #             "source": script_txt,
        #             "lang": "painless"
        #         }
        #     })
        # print(bulk_data_update)
        # bulk(self.elas, bulk_data_update, raise_on_error=True)

    def get_items(self, index_name, page, size, str_query="", lst_text_field_for_query = []):  
        page = max(page, 0)
        from_ = (page - 1) * size  
        result = []
        should_conditions = []
        
        # must = [
        #     {
        #         "match_phrase": {
        #                 
        #             } 
        #     }
        # ]
        if str_query:
            for field in lst_text_field:
                should_conditions.append({
                    "match_phrase": {
                        field: str_query
                    }
                })
        query = {
            "bool": {
                "should": should_conditions,
                # "must": must
            }
        }
        print(query)
        sort = 'last_updated_time:desc'

        scroll_id = None
        scroll = "5m"

        total = self.elas.count(index=index_name, body={"query": query})["count"]
   
        res_ = self.elas.search(index=index_name, body={"query": query}, size=size, from_=from_)#, sort=sort)
        result = res_["hits"]["hits"]
        final_result = []
        for r in result:
            out = r['_source']
            out["id"] = r["_id"]
            final_result.append(out)
        # while True:
        #     try:
        #         if scroll_id is None:
        #             self.elas.indices.refresh(index=index_name)
        #             res_ = self.elas.search(index=index_name, body={"query": query}, size=size, from_=from_, scroll=scroll)#, sort=sort)
        #         else:
        #             res_ = self.elas.scroll(scroll_id=scroll_id, scroll=scroll)

        #         scroll_id = res_["_scroll_id"]
        #         print(scroll_id)
        #         dt= res_["hits"]["hits"]
                
        #         result.extend(dt)
        #         print(len(result))  
        #         if len(dt) == 0:
        #             self.elas.clear_scroll(scroll_id=scroll_id)
        #             break
        #     except Exception as ex:
        #         print(ex)
        return final_result, total

if __name__ == '__main__':
    client = OpenDomainConfigElasticsearch()
    client.create_index(client.config_index)