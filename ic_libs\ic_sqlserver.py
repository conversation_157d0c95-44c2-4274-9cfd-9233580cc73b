# coding: utf-8
import platform
import pyodbc
from contextlib import contextmanager

def is_valid_key(key):
    """
    check t<PERSON>h hợp lệ
    :param key: key đầu vào
    :return: True or False
    """
    for c in key:
        if c.isspace():
            return False
    return True

def parse_config(filepath):
    """
    parse config
    :param filepath: đường dẫn file config
    :return: dictionary config
    """
    dict_k = {}
    with open(filepath) as f:
        for lineno, line in enumerate(f, 1):
            line = line.strip()
            if line.startswith('#') or line.startswith(';') or line.startswith('//') or not line:
                continue
            k, v = line.split('=', 1)
            k = k.strip()
            v = v.strip()

            k = k.lower()

            if not k or not is_valid_key(k):
                raise RuntimeError("Config has problem at line {} {}".format(lineno, filepath))
            try:
                v = int(v)
            except ValueError:
                try:
                    v = float(v)
                except ValueError:
                    pass
            dict_k[k] = v
    return dict_k

def parse_yml_config(config_param):
    return config_param

class DatabaseIO(object):
    def __init__(self, filename_conf='config/sql_autotag.cfg'):
        """
        khởi tạo connection
        :param filename_conf: đường dẫn file config
        """
        self.connection_metadata = self.load_db_config(filename_conf)

    def load_db_config(self, filepath):
        """
        load config db
        :param filepath: đường dẫn file config
        :return: dictionary config
        """
        # ccr_conf = parse_config(filepath)
        ccr_conf = parse_yml_config(filepath)
        return DatabaseIO.get_connection_metadata(ccr_conf)

    @staticmethod
    def get_connection_metadata(ccr_conf):
        """
        parsing config db
        :param ccr_conf: dictionary đọc từ file config
        :return: dictionary config db
        """
        return {
            'server': ccr_conf['server'],
            'port': ccr_conf['port'],
            'user': str(ccr_conf['user']),
            'password': str(ccr_conf['password']),
            'database': str(ccr_conf['database'])
        }

    @contextmanager
    def connect_database(self):
        """
        Kết nối với DB, hàm sẽ tự động đóng kết nối và commit dữ liệu khi kết thúc. Đóng connection khi kết thúc
        Sử dụng theo mẫu
            with connect_database() as conn:
                with conn.cursor() as cursor:
                    try:
                        cursor.execute(SQL_QUERY, ...)
                    except:
                        ...
        :return: trả về generator
        """
        cnxn = None
        if 'driver' not in self.connection_metadata:
            system = platform.system()
            driver = "{FreeTDS}"
            if system == 'Linux':
                driver = "{ODBC Driver 17 for SQL Server}"  # xem ở /etc/odbcinst.ini
            elif system == 'Windows':
                driver = "{SQL Server}"  # xem ở Control Panel > Administrator Tools > Data Source > Drivers
            self.connection_metadata['driver'] = driver
        conn_str = 'DRIVER=%(driver)s;SERVER=%(server)s;PORT=%(port)d;DATABASE=%(database)s;UID=%(user)s;PWD=%(password)s' \
                   % self.connection_metadata
        try:
            cnxn = pyodbc.connect(conn_str, unicode_results=True)
            yield cnxn
        finally:
            if cnxn:
                cnxn.close()

    def connect_database_keep(self):
        """
        Kết nối với DB, hàm sẽ tự động đóng kết nối và commit dữ liệu khi kết thúc. Không đóng connection khi kết thúc
        Sử dụng theo mẫu
            with connect_database() as conn:
                with conn.cursor() as cursor:
                    try:
                        cursor.execute(SQL_QUERY, ...)
                    except:
                        ...
        :return: trả về generator
        """
        cnxn = None
        if 'driver' not in self.connection_metadata:
            system = platform.system()
            driver = "{FreeTDS}"
            if system == 'Linux':
                driver = "{ODBC Driver 17 for SQL Server}"  # xem ở /etc/odbcinst.ini
            elif system == 'Windows':
                driver = "{SQL Server}"  # xem ở Control Panel > Administrator Tools > Data Source > Drivers
            self.connection_metadata['driver'] = driver
        conn_str = 'DRIVER=%(driver)s;SERVER=%(server)s;PORT=%(port)d;DATABASE=%(database)s;UID=%(user)s;PWD=%(password)s' % self.connection_metadata
        while True:
            try:
                cnxn = pyodbc.connect(conn_str, unicode_results=True,autocommit=True)
                break
            except Exception as ve:
                print(ve)
        return cnxn


    def query_db(self, sql_query, *args):
        """
        query db (chỉ dùng cho select)
        :param sql_query: query string
        :param args: tham số truyền vào
        :return: con trỏ data
        """
        with self.connect_database() as conn:
            with conn.cursor() as cursor:
                try:
                    cursor.execute(sql_query, *args)
                except pyodbc.DataError as de:
                    self.logger.error("{0}".format(de))
                    conn.rollback()
                else:
                    for record in cursor:
                        yield record

    def query_insert_db(self, sql_query, *args):
        """
        query db (dùng cho insert or update)
        :param sql_query: query string
        :param args: tham số truyền vào
        :return:
        """
        with self.connect_database() as conn:
            with conn.cursor() as cursor:
                try:
                    cursor.execute(sql_query, *args)
                except Exception as ve:
                    print (ve)
    
    def query_insert_db_keep_connect(self, conn, sql_query, *args):
        """
        query db (dùng cho insert update). Mục đích giữ kết nối database
        :param conn: connection đến database
        :param sql_query: query string
        :param args: tham số truyền vào
        :return:
        """
        with conn.cursor() as cursor:
            try:
                cursor.execute(sql_query, *args)
            except Exception as ve:
                print(ve)

    def query_db_keep_connect(self, conn, sql_query, *args):
        """
        query db (chỉ dùng cho select), Mục đích giữ kết nối database
        :param conn: connection đến database
        :param sql_query: query string
        :param args: tham số truyền vào
        :return: con trả data
        """
        with conn.cursor() as cursor:
            try:
                cursor.execute(sql_query, *args)
            except pyodbc.DataError as de:
                self.logger.error("{0}".format(de))
                conn.rollback()
            else:
                for record in cursor:
                    yield record

if __name__ == "__main__":
    pass
    # dbio = DatabaseIO(filename_conf=r'D:\Project\autotag\autotag\src\config\sql_facebook_info.cfg')
    # cur = dbio.query_db("""SELECT Distinct [FBId]
    #                         FROM [facebook_info].[dbo].[UserPhone] WITH(NOLOCK)""")
    # with open(r'C:\Users\<USER>\Desktop\fbid.txt',"w+") as f:
    #     for record in cur:
    #         print(record.FBId)
    #         f.write(str(record.FBId) + "\n")