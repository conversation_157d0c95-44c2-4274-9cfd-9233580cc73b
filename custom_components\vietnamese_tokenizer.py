# custom_components/vn_tokenizer.py

from typing import Any, Dict, List, Text, Type

from rasa.nlu.tokenizers.tokenizer import Tokenizer
from rasa.shared.nlu.tokenizers.token import Token
from rasa.shared.nlu.training_data.message import Message
from rasa.shared.nlu.constants import TEXT

from rasa.engine.recipes.default_recipe import DefaultV1R<PERSON>ipe
from rasa.engine.storage.resource import Resource
from rasa.engine.storage.storage import ModelStorage

import underthesea


@DefaultV1Recipe.register(
    component_types=[Tokenizer], is_trainable=False
)
class VietnameseTokenizer(Tokenizer):
    """Custom Vietnamese tokenizer using underthesea"""

    @classmethod
    def required_components(cls) -> List[Type]:
        return []

    def __init__(
        self,
        config: Dict[Text, Any],
        name: Text,
        model_storage: ModelStorage,
        resource: Resource,
    ) -> None:
        super().__init__(config)

    def tokenize(self, message: Message, attribute: Text = TEXT) -> List[Token]:
        text = message.get(attribute)

        # Tách từ với underthesea (dạng chuỗi "tôi muốn mua | áo thun | màu đỏ")
        words = underthesea.word_tokenize(text, format="text").split()

        tokens = []
        running_offset = 0
        for word in words:
            start = text.find(word, running_offset)
            if start == -1:
                continue
            tokens.append(Token(text=word, start=start))
            running_offset = start + len(word)

        return self._apply_token_pattern(tokens)
