from ic_libs.ic_mongo import MongoDatabaseIO

class ChatbotMongoDb():
    def __init__(self, host="**********", port=27017, database_name="SocialBuzz", max_history = 10):
        self.host = host
        self.port = port
        self.database_name = database_name
        self.collection_chatbot = "Chatbot"
        self.max_history = 10
        self.db_client = MongoDatabaseIO(host=host, port=port, database_name=database_name)

    def delete_conversation(self, user_id):
        regex_pattern = f'^{user_id}'
        self.db_client.delete_document( collection_name=self.collection_chatbot, dic_filter={'conversation_id': {'$regex': regex_pattern}})

    def search_conversation(self, conversation_id):
        res = self.db_client.find_one(collection_name=self.collection_chatbot, dict_query= {"conversation_id": conversation_id})
        return res
    
    def insert_conversation(self, conversation_id, history, last_message_time, is_reply = False):
        conversation = self.search_conversation(conversation_id)
        if not conversation:
            doc_insert = {
                "conversation_id": conversation_id,
                "history": history,
                "last_message_time": last_message_time,
                "is_reply": is_reply
            }
            self.db_client.insert_one_doc(collection_name=self.collection_chatbot, data_insert=doc_insert)
        else:
            self.update_conversation(conversation_id, history=history, last_message_time=last_message_time, is_reply=is_reply, conversation=conversation)
    def update_conversation(self, conversation_id, history, last_message_time, is_reply = False, conversation = None):
        if not conversation:
            conversation = self.search_conversation(conversation_id)
        conversation["history"] = history
        conversation["last_message_time"] = last_message_time
        conversation["is_reply"] = is_reply
        self.db_client.update_value(collection_name=self.collection_chatbot, dic_filter={"conversation_id": conversation_id}, update={"$set": conversation})

    def truncate_history(self, history):
        if self.max_history > 0:
            history = history[-self.max_history:]
        return history

    def append_conversatiton(self, conversation_id, role, text, created_time, is_reply=False, conversation=None):
        history = []
        if conversation is None:
            conversation = self.search_conversation(conversation_id)
        if conversation:
            history = conversation["history"]
            history.append({
                "role": role,
                "text": text,
                "created_time": created_time
            })
            history = self.truncate_history(history)
            self.update_conversation(conversation_id, history, last_message_time=created_time, is_reply=is_reply)
        else:
            history.append({
                "role": role,
                "text": text,
                "created_time": created_time
            })
            self.insert_conversation(conversation_id, history, last_message_time=created_time)
        return history



        
def save_conversation_history(conversation_id, role, text, created_time, ):
    global dict_history
    if conversation_id not in dict_history:
        dict_history[conversation_id] = []
    dict_history[conversation_id].append({
        # "user_id": user_id,
        "role": role,
        "text": text,
        "created_time": created_time
    })
if __name__ == '__main__':
    client = ChatbotMongoDb()
    conversation_id = "123"
    client.search_conversation(conversation_id)