import pytest
import json
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from consumer_chatbot_llm import RabbitMQConsumer, LLMChatProcessor

# Sample test message based on demo.log
SAMPLE_MESSAGE = {
    "text": "xin chào",
    "conversation_id": "t_2401012933599170",
    "page_id": "285134531496649",
    "attachments": [],
    "history": [
        {
            "text": "Muốn mở thêm tài khoản thì làm như nào?",
            "is_customer": 1
        },
        {
            "text": "Tô<PERSON> muốn mở thẻ tín dụng",
            "is_customer": 1
        }
    ],
    "platform": "facebook",
    "fb_scope_id": "9284133451692650",
    "user_name": "<PERSON>h<PERSON>",
    "is_first_message": True,
    "is_duplicate_message": False,
    "created_time": 1746777688.7408342
}

# Sample response based on demo.log
SAMPLE_RESPONSE = {
    "text": "xin chào",
    "conversation_id": "t_2401012933599170",
    "page_id": "285134531496649",
    "attachments": [],
    "history": SAMPLE_MESSAGE["history"],
    "platform": "facebook",
    "fb_scope_id": "9284133451692650",
    "user_name": "Thân Ngọc Thiện",
    "is_first_message": True,
    "is_duplicate_message": False,
    "created_time": 1746777688.7408342,
    "intent": {
        "name": "intent_25",
        "confidence": 0.9016677141189575
    },
    "entities": [],
    "intent_ranking": [
        {
            "name": "lỗi app",
            "confidence": 0.9016677141189575
        }
    ],
    "actions": "Bạn vui lòng cung cấp câu thông báo lỗi/mã lỗi chúng tôi sẽ kiểm tra và phản hồi.\nTrường hợp đã có thông báo trừ tiền bạn vui lòng thông báo người nhận kiểm tra số dư tài khoản và liên hệ Hotline miễn phí 1800 588 822 (trong nước) hoặc 84 24 3944 6699 (quốc tế) để được hỗ trợ kịp thời nhé!",
    "buttons": [],
    "message": "success",
    "error_code": 0,
    "type": "text"
}

@pytest.fixture
def mock_processor():
    """Fixture to create a mock LLMChatProcessor."""
    processor = AsyncMock(spec=LLMChatProcessor)
    processor.process_message.return_value = SAMPLE_RESPONSE
    return processor

@pytest.fixture
def consumer(mock_processor):
    """Fixture to create a RabbitMQConsumer with mocked dependencies."""
    with patch('consumer_chatbot_llm.LLMChatProcessor', return_value=mock_processor):
        consumer = RabbitMQConsumer()
        return consumer

@pytest.mark.asyncio
async def test_process_message_success(consumer, mock_processor):
    """Test successful message processing."""
    # Create mock channel and method
    mock_channel = Mock()
    mock_method = Mock()
    mock_method.delivery_tag = 1
    
    # Create message body
    message_body = json.dumps(SAMPLE_MESSAGE).encode('utf-8')
    
    print("\n=== Test Process Message Success ===")
    print("\n=== Chat History ===")
    for idx, msg in enumerate(SAMPLE_MESSAGE["history"], 1):
        print(f"\nMessage {idx}:")
        print(f"From: {'Customer' if msg['is_customer'] else 'Bot'}")
        print(f"Content: {msg['text']}")
    
    print("\n=== Current Message ===")
    print(f"From: Customer")
    print(f"Content: {SAMPLE_MESSAGE['text']}")
    
    # Process message
    await consumer.process_message(mock_channel, mock_method, None, message_body)
    
    # Get the response from the mock processor
    response = mock_processor.process_message.call_args[0][0]
    print("\n=== Bot Response ===")
    print(f"Intent: {response['intent']['name']} (confidence: {response['intent']['confidence']:.2f})")
    print(f"Top Intent Ranking:")
    for idx, intent in enumerate(response['intent_ranking'][:3], 1):
        print(f"{idx}. {intent['name']} (confidence: {intent['confidence']:.2f})")
    print(f"\nResponse Text: {response['actions']}")
    
    # Verify processor was called with correct message
    mock_processor.process_message.assert_called_once_with(SAMPLE_MESSAGE)
    
    # Verify message was acknowledged
    mock_channel.basic_ack.assert_called_once_with(delivery_tag=1)

@pytest.mark.asyncio
async def test_process_message_invalid_json(consumer):
    """Test handling of invalid JSON message."""
    # Create mock channel and method
    mock_channel = Mock()
    mock_method = Mock()
    mock_method.delivery_tag = 1
    
    # Create invalid message body
    invalid_message = b"invalid json"
    
    print("\n=== Test Process Invalid JSON ===")
    print("Invalid Message:", invalid_message.decode('utf-8'))
    print("Expected: JSON parsing error")
    
    # Process message
    await consumer.process_message(mock_channel, mock_method, None, invalid_message)
    
    # Verify message was rejected
    mock_channel.basic_nack.assert_called_once_with(delivery_tag=1)

@pytest.mark.asyncio
async def test_process_message_processor_error(consumer, mock_processor):
    """Test handling of processor errors."""
    # Create mock channel and method
    mock_channel = Mock()
    mock_method = Mock()
    mock_method.delivery_tag = 1
    
    # Make processor raise an exception
    mock_processor.process_message.side_effect = Exception("Processor error")
    
    # Create message body
    message_body = json.dumps(SAMPLE_MESSAGE).encode('utf-8')
    
    print("\n=== Test Process Message Error ===")
    print("\n=== Chat History ===")
    for idx, msg in enumerate(SAMPLE_MESSAGE["history"], 1):
        print(f"\nMessage {idx}:")
        print(f"From: {'Customer' if msg['is_customer'] else 'Bot'}")
        print(f"Content: {msg['text']}")
    
    print("\n=== Current Message ===")
    print(f"From: Customer")
    print(f"Content: {SAMPLE_MESSAGE['text']}")
    print("\nExpected Error: Processor error")
    
    # Process message
    await consumer.process_message(mock_channel, mock_method, None, message_body)
    
    # Verify message was rejected
    mock_channel.basic_nack.assert_called_once_with(delivery_tag=1)

def test_setup_connection(consumer):
    """Test RabbitMQ connection setup."""
    print("\n=== Test Setup Connection ===")
    with patch('pika.BlockingConnection') as mock_connection:
        mock_channel = Mock()
        mock_connection.return_value.channel.return_value = mock_channel
        
        consumer.setup_connection()
        
        print("Connection Parameters:")
        print(f"Queue Name: {consumer.queue_name}")
        print(f"Prefetch Count: 1")
        
        # Verify connection was created
        mock_connection.assert_called_once()
        
        # Verify queue was declared
        mock_channel.queue_declare.assert_called_once()
        
        # Verify QoS was set
        mock_channel.basic_qos.assert_called_once_with(prefetch_count=1)

def test_close_connection(consumer):
    """Test closing RabbitMQ connection."""
    print("\n=== Test Close Connection ===")
    # Create mock connection with close method
    mock_connection = Mock()
    mock_connection.is_closed = False
    mock_connection.close = Mock()
    consumer.connection = mock_connection
    
    print("Connection Status: Not Closed")
    
    # Close connection
    consumer.close()
    
    print("Connection Status: Closed")
    
    # Verify connection was closed
    mock_connection.close.assert_called_once() 