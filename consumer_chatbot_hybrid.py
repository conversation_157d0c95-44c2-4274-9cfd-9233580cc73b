import pika
import json
import asyncio
import functools
import logging
import datetime
from typing import Dict, Any, Set
from rabbit_common import ICRabbitMQ, queue_chatbot_message_params, queue_chatbot_message_params_dev
from function.process_messages_llm import LLMChatProcessor, DEFAULT_CONFIG as LLM_DEFAULT_CONFIG
from function.process_messages import RasaChatProcessor, DEFAULT_CONFIG as RASA_DEFAULT_CONFIG
from function.save_result import save_hybrid_results_to_excel

# Logging setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)
LOG_FILE_PATH = "chat_conversation_log_hybrid.txt"

# Global state
processed_messages: Set[str] = set()
last_message_content: Dict[str, str] = {}

# Initialize chat processors
try:
    llm_chat_processor = LLMChatProcessor(config=LLM_DEFAULT_CONFIG)
    logger.info("LLMChatProcessor initialized successfully.")
except Exception as e:
    logger.critical("LLMChatProcessor failed to initialize", exc_info=True)
    exit(1)

try:
    rasa_chat_processor = RasaChatProcessor(config=RASA_DEFAULT_CONFIG)
    logger.info("RasaChatProcessor initialized successfully.")
except Exception as e:
    logger.critical("RasaChatProcessor failed to initialize", exc_info=True)
    exit(1)

if not rasa_chat_processor.agent:
    logger.critical("Rasa agent failed to load. Exiting...")
    exit(1)

def sync(f):
    @functools.wraps(f)
    def wrapper(*args, **kwargs):
        try:
            loop = asyncio.get_event_loop_policy().get_event_loop()
            if loop.is_closed():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        return loop.run_until_complete(f(*args, **kwargs))
    return wrapper


def log_message(received: Dict[str, Any], response: Dict[str, Any]):
    try:
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        with open(LOG_FILE_PATH, "a", encoding="utf-8") as f:
            f.write(f"[{timestamp}] Received:\n{json.dumps(received, ensure_ascii=False, indent=2)}\n")
            f.write(f"[{timestamp}] Response:\n{json.dumps(response, ensure_ascii=False, indent=2)}\n---\n")
    except Exception as e:
        logger.error(f"Failed to write to log file: {e}")


def is_duplicate_message(message_id: str, user_id: str, content: str) -> bool:
    if message_id and message_id in processed_messages:
        return True
    if user_id in last_message_content and last_message_content[user_id] == content:
        return True
    return False


def update_tracking(message_id: str, user_id: str, content: str):
    if message_id:
        processed_messages.add(message_id)
        if len(processed_messages) > 10000:
            processed_messages.pop() # Consider a more robust way to manage size, like a deque
    if content:
        last_message_content[user_id] = content


@sync
async def callback_func(ch, method, properties, body):
    delivery_tag = method.delivery_tag
    logger.info(f"Received message. Delivery Tag: {delivery_tag}")
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            body_data = json.loads(body.decode("utf-8"))
            user_id = body_data.get('user_ID', body_data.get('conversation_id', 'default_user'))
            message_id = body_data.get('message_id')
            message_content = body_data.get('text', '').strip()
            is_web_req_flag = body_data.get("is_web_request", True)

            if is_duplicate_message(message_id, user_id, message_content):
                logger.info(f"Duplicate detected. Acking tag: {delivery_tag}")
                ch.basic_ack(delivery_tag=delivery_tag)
                return
            
            # Process with Rasa first
            rasa_processed_result = await rasa_chat_processor.process_message(body_data, is_web_request=is_web_req_flag)
            
            rasa_top_intent = rasa_processed_result.get('intent', {}).get('name')
            rasa_top_confidence = rasa_processed_result.get('intent', {}).get('confidence', 0.0)
            
            final_processed_result = {}

            selected_model_name = ""
            if rasa_top_confidence >= rasa_chat_processor.config["min_confidence_for_direct_action"] and rasa_top_intent != "nlu_fallback":
                logger.info(f"Rasa confident ({rasa_top_confidence:.2f}) with intent '{rasa_top_intent}'. Using Rasa's response.")
                final_processed_result = rasa_processed_result
                selected_model_name = "Rasa"
            else:
                # If Rasa is not confident or is a fallback, try LLM
                logger.info(f"Rasa not confident (confidence: {rasa_top_confidence:.2f}, intent: {rasa_top_intent}). Processing with LLM.")
                llm_processed_result = await llm_chat_processor.process_message(body_data, is_web_request=is_web_req_flag)
                final_processed_result = llm_processed_result
                selected_model_name = "LLM"

            if "status" not in final_processed_result:
                final_processed_result["status"] = 1
            if "message" not in final_processed_result:
                final_processed_result["message"] = "processed_by_hybrid_consumer"
            if "action" not in final_processed_result:
                final_processed_result["action"] = {}

            body_data["parsed_intent"] = final_processed_result.get("intent", {})
            body_data["parsed_entities"] = final_processed_result.get("entities", [])
            body_data["parsed_intent_ranking"] = final_processed_result.get("intent_ranking", [])

            update_tracking(message_id, user_id, message_content)
            log_message(body_data, final_processed_result)
            # Save to Excel instead of API
            save_hybrid_results_to_excel(body_data, rasa_processed_result, llm_processed_result, final_processed_result)

            ch.basic_ack(delivery_tag=delivery_tag)
            logger.info(f"Processing complete for tag {delivery_tag}")
            break

        except json.JSONDecodeError as e:
            logger.error(f"JSON error at tag {delivery_tag}: {e}. Nacking without requeue.")
            ch.basic_nack(delivery_tag=delivery_tag, requeue=False)
            break

        except Exception as e:
            retry_count += 1
            logger.error(f"Error processing tag {delivery_tag} (Attempt {retry_count}): {e}", exc_info=True)
            if retry_count >= max_retries:
                logger.error(f"Max retries reached. Nacking tag {delivery_tag}.")
                ch.basic_nack(delivery_tag=delivery_tag, requeue=False)
                break
            await asyncio.sleep(1 * retry_count)


def start_consumer():
    logger.info("Starting RabbitMQ Consumer for Hybrid Chat Processor...")

    usr_name = queue_chatbot_message_params_dev["UserName"]
    password = str(queue_chatbot_message_params_dev["Password"])
    host = queue_chatbot_message_params_dev["HostName"]
    virtual_host = queue_chatbot_message_params_dev["VirtualHost"]
    queue_name = queue_chatbot_message_params_dev["Queue"]
    prefetch_count = queue_chatbot_message_params_dev.get('PrefetchCount', 1)

    max_retry_attempts = 5
    retry_delay = 1
    retry_count = 0

    while True:
        try:
            credentials = pika.PlainCredentials(usr_name, password)
            connection_params = pika.ConnectionParameters(
                host=host,
                virtual_host=virtual_host,
                credentials=credentials,
                heartbeat=600,
                blocked_connection_timeout=300,
                retry_delay=1,
                connection_attempts=3
            )
            connection = pika.BlockingConnection(connection_params)
            channel = connection.channel()

            channel.queue_declare(queue=queue_name, durable=True, arguments={"x-max-priority": 10})
            channel.basic_qos(prefetch_count=prefetch_count)
            channel.basic_consume(queue=queue_name, on_message_callback=callback_func)

            logger.info(f"Connected. Waiting for messages on '{queue_name}'")
            channel.start_consuming()

        except pika.exceptions.AMQPConnectionError as e:
            retry_count += 1
            if retry_count >= max_retry_attempts:
                logger.error(f"Max retry attempts reached: {e}")
                break
            logger.warning(f"Connection error (Attempt {retry_count}): Retrying in {retry_delay}s...")
            asyncio.run(asyncio.sleep(retry_delay))
            retry_delay = min(retry_delay * 2, 60)

        except Exception as e:
            logger.error(f"Unexpected error: {e}", exc_info=True)
            asyncio.run(asyncio.sleep(10))


if __name__ == '__main__':
    if not llm_chat_processor or not rasa_chat_processor:
        logger.critical("One or both chat processors not initialized. Exiting.")
        exit(1)

    start_consumer() 