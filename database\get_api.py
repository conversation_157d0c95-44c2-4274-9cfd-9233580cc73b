import requests
import numpy as np 

EMBED_URL_SEMANTIC_OLD = "http://**********:6969/embed"
EMBED_URL_SENT2VEC = "http://**********:6969/embed_sent2vec"

EMBED_URL_SEMANTIC = "http://**********:2807/sbert_embed"

def embed_sentences(texts, url = EMBED_URL_SEMANTIC_OLD):
    if not url.strip():
        url = EMBED_URL_SEMANTIC_OLD
    resp = requests.post(url, json = {"texts":texts, "normalize": True})
    # return np.array(resp.json()["embeddings"])
    return resp.json()["embeddings"]


def embed_sentences_sent2vec(texts):
    resp = requests.post(EMBED_URL_SENT2VEC, json = {"texts":texts, "normalize": True})
    # return np.array(resp.json()["embeddings"])
    return resp.json()["embeddings"]