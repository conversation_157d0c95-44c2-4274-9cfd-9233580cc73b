# Lọc các dòng có chứa cụm từ "doanh nghiệp"
input_file = "output_deduplicated_new.txt"
output_file = "data_example_intent/output_so_cua_tech.txt"

keyword = "số của tech"

with open(input_file, "r", encoding="utf-8") as f:
    lines = [line.strip() for line in f if keyword.lower() in line.lower()]

with open(output_file, "w", encoding="utf-8") as f:
    for line in lines:
        f.write(line + "\n")

print(f"✅ Đã lọc {len(lines)} dòng chứa cụm từ '{keyword}' vào '{output_file}'")