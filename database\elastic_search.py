from .get_api import embed_sentences, embed_sentences_sent2vec
from .common_config import CommonConfigClient
from elasticsearch import Elasticsearch
from elasticsearch.helpers.actions import bulk

import requests
import json
import time
import hashlib
import nltk
import copy
import numpy as np

common_config_client = CommonConfigClient()

URL_ADD_ITEM = "http://10.9.3.241:6262/add_item"
URL_UPDATE_ITEM = "http://10.9.3.241:6262/update_item"

DEFAULT_CONFIG_ELAS = {
    "mappings": {
        "Query": {"type": "text"},
        "AllTextFieldsLowered": {"type": "text"},
        "Text": {"type": "text"},
        "creator": {"type": "text"},
        "customer": {"type": "keyword"},
        "created_time": {"type": "long"},
        "EmbeddingQuery": {
                "type": "dense_vector",
                "dims": 768,
                "index": True,
                "similarity": "cosine",
                "index_options": {
                    "type": "hnsw",
                    "m": 64,
                    "ef_construction": 512
                }
            },
        "EmbeddingQuerySent2vec": {
                "type": "dense_vector",
                "dims": 384,
                "index": True,
                "similarity": "cosine",
                "index_options": {
                    "type": "hnsw",
                    "m": 64,
                    "ef_construction": 512
                }
            },
        "EmbeddingText": {
                "type": "dense_vector",
                "dims": 768,
                "index": True,
                "similarity": "cosine",
                "index_options": {
                    "type": "hnsw",
                    "m": 64,
                    "ef_construction": 512
                }
            }
    },
    "settings": {
    "number_of_shards": 1,
    "index" : {
        "similarity" : {
        "default" : {
            "type" : "BM25",
            "b": 0.75,
            "k1": 1.2
        }
        }
    }
}
}

class FaqElasticsearch():
    def __init__(self, host = "http://**********:9200", timeout=30, max_retries=10, retry_on_timeout=True ):
        self.elas = Elasticsearch(hosts=host, timeout=timeout, max_retries=max_retries, retry_on_timeout=retry_on_timeout)
        self.faq_index = "qna_chatbot_staging"
        self.default_config = DEFAULT_CONFIG_ELAS
        self.document_handler = DocumentHandler()
        self.PATH_GET_ITEMS = "/list"
        self.PATH_CREATE_BULK = "/create_bulk"
        self.PATH_UPDATE_BULK = "/update_bulk"
        self.PATH_DELETE_BULK = "/delete_bulk"

    def create_index(self, index_name, mappings=None, settings =None):
        if  not self.elas.indices.exists(index=index_name):
            if not mappings:
                mappings = {"properties": self.default_config["mappings"]}
            if not settings:
                settings = self.default_config["settings"]
            self.elas.indices.create(index = index_name, mappings=mappings, settings=settings)

    def encode(url_extract_feature, texts, lang='vi'):
        json_body = {
            "texts": texts,
            "normalize": True
        }
        result = None
        try:
            res = requests.post(url=url_extract_feature, json=json_body)
            res = res.json()
            result = res["embeddings"]
        except Exception as ex:
            print(f"[ERROR] encode sentences: {ex}")
        return result

    def get_api_connect(self, customer, postfix_url, customer_config = None):
        if not customer_config:
            customer_config = common_config_client.get_common_config_full(customer_id=customer)
        api_connect = customer_config.get("api_connect", "")
        if api_connect.strip():
            api_connect += postfix_url
        return api_connect

    def get_api_list_items(self, url, page, size, customer, query = ""):
        json_body = {
            "page": page,
            "size": size,
            "query": query.strip(),
            "customer": customer
        }
        final_result, total = [], 0
        try:
            res = requests.post(url, json=json_body)
            res = res.json()
            total = res.get("total", size)  ## default value = size for test 
            final_result = res["result"]
        except Exception as ex:
            print(f"[ERROR] get_api_list_items {url} {customer}: {str(ex)}")
        return final_result, total

    def get_api_create_bulk(self, url, data:list):
        json_body = data
        message = "sucess"
        try:
            res = requests.post(url, json=json_body)
            res = res.json()
            print("[INFO] get_api_create_bulk: ", res)
            
        except Exception as ex:
            print(f"[ERROR] get_api_create_bulk {url} {customer}: {str(ex)}")
            message = "error"
        return {"message": resp}

    def get_api_update_bulk(self, url, data:list):
        json_body = data
        try:
            res = requests.post(url, json=json_body)
            res = res.json()
            print(f"[INFO] get_api_update_bulk {url} {customer}: ", res)
            
        except Exception as ex:
            print(f"[ERROR] get_api_create_bulk {url} {customer}: {str(ex)}")
            message = "error"
        return {"message": resp}

    def create_record(self, record, index_name, window_size=3, overlap=2, customer_config = {}):
        url_embed_sentences = customer_config.get("url_embed", "")
        query = record["Query"]
        fulltext = record["Text"]
        score = record.get("score", 0.5)
        customer_id = record["customer"]
        creator = record.get("creator", "")
        keyword = record.get("keyword", "")
        doc_id = self.document_handler.get_document_id(query)
        sentences_fulltext = nltk.sent_tokenize(fulltext)
        query_embedding = embed_sentences([query], url = url_embed_sentences)[0]
        query_embedding_sent2vec = embed_sentences_sent2vec([query])[0]

        bulk_data = []

        if len(keyword.strip()) > 0:
            paras = [fulltext]
        else:
            paras = self.document_handler.passage_blocks(fulltext, window_size, overlap)
        text_embeddings = embed_sentences(paras, url=url_embed_sentences)
        for i,para in enumerate(paras):

            text_embedding = text_embeddings[i]
            insert_time = int(time.time())
            bulk_data.append({
                "_id": doc_id + "_" + str(i),
                "_index": index_name,
                "_source": {   
                    "Query": query,
                    "Text": para,
                    "keyword": keyword,
                    "score": score,
                    "creator": creator,
                    "customer": customer_id,
                    "created_time": insert_time,
                    "last_updated_time": insert_time,
                    "reference_text": fulltext,
                    "AllTextFieldsLowered": query + ". " + para,
                    "EmbeddingQuery": query_embedding,
                    "EmbeddingQuerySent2vec": query_embedding_sent2vec,
                    "EmbeddingText": text_embedding
                }
            })
        return bulk_data

    def add_item_bulk(self, index_name, datas, customer_config = {}):
        customer = data["customer"]
        customer_config = common_config_client.get_common_config_full(customer_id=customer)
        api_connect = self.get_api_connect(customer, postfix_url=self.PATH_CREATE_BULK, customer_config=customer_config)
        if api_connect.strip():
            return self.get_api_create_bulk(api_connect, data)

    def add_item(self, index_name, data, customer_config = {}):
        customer = data["customer"]
        customer_config = common_config_client.get_common_config_full(customer_id=customer)
        api_connect = self.get_api_connect(customer, postfix_url=self.PATH_CREATE_BULK, customer_config=customer_config)
        if api_connect.strip():
            return self.get_api_create_bulk(api_connect, [data])

        self.create_index(index_name)
        bulk_data = self.create_record(record=data, index_name=index_name, customer_config = customer_config)
        resp = bulk(self.elas, bulk_data, refresh='wait_for')
        print(resp)
        return {"message": resp}

    def delete_items(self, index_name, ids):
        data = []
        for id_ in ids:
            d = {
                "_op_type": "delete",
                "_id": id_,
                "_index": index_name,
            }
            data.append(d)
        bulk(self.elas, data, refresh='wait_for')

    def update_items(self, items, index_name):
        '''[
        {
            "Query": "string",
            "Text": "string",
            "customer": "string",
            "score": 1,
            "keyword": "",
            "id": "string"
        }
        ]'''

        bulk_data = []
        dict_customer_config = {}

        bulk_customer_data = {}
        for item in items:
            customer_id = item["customer"]
            if customer_id not in dict_customer_config:
                customer_config = common_config_client.get_common_config_full(customer_id=customer_id)
                dict_customer_config[customer_id] = customer_config
            else:
                customer_config = dict_customer_config[customer_id]
            if customer_id not in bulk_customer_data:
                bulk_customer_data[customer_id] = []
            bulk_customer_data.append[customer_id].append(item)
        
        for customer_id in dict_customer_config:
            api_connect = self.get_api_connect(customer_id, postfix_url=self.PATH_UPDATE_BULK)
            if api_connect.strip():
                self.get_api_update_bulk(index_name, bulk_customer_data[customer_id])
                bulk_customer_data[customer_id] = []
        
        rest_ỉtems = []
        for customer_id in bulk_customer_data:
            rest_ỉtems.extend(bulk_customer_data[customer_id])

        for item in rest_ỉtems:
            customer_id = item["customer"]
            if customer_id not in dict_customer_config:
                customer_config = common_config_client.get_common_config_full(customer_id=customer_id)
                dict_customer_config[customer_id] = customer_config
            else:
                customer_config = dict_customer_config[customer_id]
            
            url_embed_sentences = customer_config.get("url_embed", "")

            id_ = item["id"]
            query = item["Query"]
            text = item["Text"]
            query_embedding = embed_sentences([query], url = url_embed_sentences)[0]
            query_embedding_sent2vec = embed_sentences_sent2vec([query])[0]
            text_embedding = embed_sentences([text], url = url_embed_sentences)[0]

            update_body = self.get_by_id(id_, index_name)
            update_body.update(item)
            update_body.pop("id",None)
            if update_body:
                update_body["EmbeddingQuery"] = query_embedding
                update_body["EmbeddingQuerySent2vec"] = query_embedding_sent2vec
                update_body["EmbeddingText"] = text_embedding
                update_body["AllTextFieldsLowered"] = query + ". " + text
                update_body["last_updated_time"] = int(time.time())
                d = {
                    "_op_type": "update",
                    "_id": id_,
                    "_index": index_name,
                    "doc": update_body
                }
                with open("a.json", 'w') as f:
                    json.dump(d , f ,ensure_ascii=False)
                bulk_data.append(d)
        bulk(self.elas, bulk_data, refresh='wait_for', raise_on_error=True)

    def get_by_id(self, id_, index_name):
        res = None
        try:
            res = self.elas.get(index=index_name, id=id_)["_source"]
        except Exception as ex:
            print(f"[ERROR] Index {index_name} get id {id_}: {str(ex)}")
        return res

    def get_items(self, customer, index_name, page, size, str_query=""):  
        api_connect = self.get_api_connect(customer, postfix_url=self.PATH_GET_ITEMS)
        if api_connect.strip():
            return self.get_api_list_items(url=api_connect, page=page, size=size, customer=customer)

        page = max(page, 0)
        from_ = (page - 1) * size  
        result = []
        should_conditions = []
        lst_text_field = ["Query", "Text"]
        must = [
            {
                "match_phrase": {
                        "customer": customer
                    } 
            }
        ]
        if str_query:
            for field in lst_text_field:
                should_conditions.append({
                    "match_phrase": {
                        field: str_query
                    }
                })
        query = {
            "bool": {
                "should": should_conditions,
                "must": must
            }
        }
        print(query)
        sort = 'last_updated_time:desc'

        scroll_id = None
        scroll = "5m"

        total = self.elas.count(index=index_name, body={"query": query})["count"]
   
        res_ = self.elas.search(index=index_name, body={"query": query}, size=size, from_=from_)#, sort=sort)
        result = res_["hits"]["hits"]
        final_result = []
        for r in result:
            out = r['_source']
            out.pop("Embedding", None)
            out["id"] = r["_id"]
            final_result.append(out)
        # while True:
        #     try:
        #         if scroll_id is None:
        #             self.elas.indices.refresh(index=index_name)
        #             res_ = self.elas.search(index=index_name, body={"query": query}, size=size, from_=from_, scroll=scroll)#, sort=sort)
        #         else:
        #             res_ = self.elas.scroll(scroll_id=scroll_id, scroll=scroll)

        #         scroll_id = res_["_scroll_id"]
        #         print(scroll_id)
        #         dt= res_["hits"]["hits"]
                
        #         result.extend(dt)
        #         print(len(result))  
        #         if len(dt) == 0:
        #             self.elas.clear_scroll(scroll_id=scroll_id)
        #             break
        #     except Exception as ex:
        #         print(ex)
        return final_result, total

    def save_result_to_dict_ids(self, result, score_name, outputs = {}):
        for r in result:
            if r["_id"] not in outputs:
                r[score_name] = r["_score"]
                outputs[r["_id"]] = r
            else:
                outputs[r["_id"]][score_name] = r["_score"]
        return outputs

    def search_item(self, index_name, query,  customer, top_k=5, num_candidates = 100, keywords = [], sortby = "mrr", use_only_query = True, semantic_weight = 0.5, customer_config = {}):
        customer_config = common_config_client.get_common_config_full(customer_id=customer)
        url_embed_sentences = customer_config.get("url_embed", "")
        start = time.time()
        dict_result_ids = {}

        top_k = num_candidates

        query_vector = embed_sentences([query], url=url_embed_sentences)[0]
        query_vector_sent2vec = embed_sentences_sent2vec([query])[0]

        keyword_dict = {
            "bool": {
            "must": [
                {
                    "match": {
                        "AllTextFieldsLowered": query
                    }
                }],
            "filter": [ 
                {"term": {"customer": customer}}
                ]
            }
        }
        
        filter_query =[{"term": {"customer": customer}}]

        
        # search by keyword
        keyword_query_result = self.elas.search(
            index=index_name, 
            body={"query": keyword_dict}, size = num_candidates
        )["hits"]["hits"]
        keyword_query_result = self.get_boosting_score(keyword_query_result)
        keyword_query_result_ids = [x["_id"] for x in keyword_query_result]
        dict_result_ids = self.save_result_to_dict_ids(keyword_query_result, score_name="score_keyword_query", outputs=dict_result_ids)

        #search knn query
        knn_query_query_result = self.filter_knn_search(query_vector, field_embedding="EmbeddingQuery", k=num_candidates, num_candidates=num_candidates, filter=filter_query, index_name=index_name)
        knn_query_query_result = [x for x in knn_query_query_result if x["_id"] in keyword_query_result_ids]
        knn_query_query_result_ids = [x["_id"] for x in knn_query_query_result]
        dict_result_ids = self.save_result_to_dict_ids(knn_query_query_result, score_name="score_semantic_query_query", outputs=dict_result_ids)

        knn_query_query_result_sent2vec = self.filter_knn_search(query_vector_sent2vec, field_embedding="EmbeddingQuerySent2vec", k=num_candidates, num_candidates=num_candidates, filter=filter_query, index_name=index_name)
        knn_query_query_result_sent2vec = [x for x in knn_query_query_result_sent2vec if x["_id"] in keyword_query_result_ids]
        knn_query_query_result_sent2vec_ids = [x["_id"] for x in knn_query_query_result_sent2vec]
        dict_result_ids = self.save_result_to_dict_ids(knn_query_query_result_sent2vec, score_name="score_sent2vec_query_query", outputs=dict_result_ids)
        
        if not use_only_query or sortby == "semantic":
            knn_query_text_result = self.filter_knn_search(query_vector, field_embedding="EmbeddingText", k=num_candidates, num_candidates=num_candidates, filter=filter_query, index_name=index_name) if not use_only_query else []
            knn_query_text_result = [x for x in knn_query_text_result if x["_id"] in keyword_query_result_ids]
            knn_query_text_result_ids = [x for x in knn_query_text_result if x["_id"] in keyword_query_result_ids]
            dict_result_ids = self.save_result_to_dict_ids(knn_query_text_result, score_name="score_semantic_query_text", outputs=dict_result_ids)

        # merge ranking list
        mrr_ranking_ids, mrr_scores = self.mrr(
        [keyword_query_result_ids, knn_query_query_result_ids, knn_query_query_result_sent2vec_ids], 
        weights = [1-semantic_weight, semantic_weight / 2, semantic_weight / 2], 
        num_candidates=num_candidates)[:top_k]

        
        if sortby == "mrr":
            final_ranking_ids = mrr_ranking_ids
            ranking_ids_indices = {final_ranking_ids[i]:i for i in range(len(final_ranking_ids))}
        elif sortby == "semantic":
            final_ranking_ids = knn_query_text_result_ids
            ranking_ids_indices = {final_ranking_ids[i]:i for i in range(len(final_ranking_ids))}
        else:
            final_ranking_ids = keyword_query_result_ids
            ranking_ids_indices = {final_ranking_ids[i]:i for i in range(len(final_ranking_ids))}

        if not final_ranking_ids: return []

        final_ranking_ids = final_ranking_ids[:top_k]
        final_results = []
        for id_ in final_ranking_ids:
            res_full = dict_result_ids[id_]
            res = res_full["_source"]
            res["ranking_score"] = ranking_ids_indices[id_] + 1
            res["semantic_score"] = max(res_full.get("score_sent2vec_query_query", 0), res_full.get("score_semantic_query_query", 0), res_full.get("score_semantic_query_text", 0))
            res["mrr_score"] = mrr_scores[id_]
            final_results.append(res)
        end = time.time()
        print("runtime:", end - start)
        final_results = list(sorted(final_results, key = lambda x: x["ranking_score"]))

        with open("b.json", 'w') as f:
            json.dump(final_results, f, ensure_ascii=False)
        return final_results
        
    def filter_knn_search(self, query_embedding, field_embedding, k, num_candidates, filter, index_name, is_boost_score = True ):
        try:
            knn_dict_query = {
                "field": field_embedding,
                "query_vector": query_embedding,
                "k": k,
                "num_candidates": num_candidates
            }

            knn_result = self.elas.knn_search(
                index=index_name, 
                knn=knn_dict_query, 
                filter = filter,
                source= {"excludes": ["EmbeddingQuery", "EmbeddingText", "EmbeddingQuerySent2vec"]}
            )["hits"]["hits"]

            if is_boost_score:
                knn_result = self.get_boosting_score(knn_result)
            
            knn_result = sorted(knn_result, key=lambda x: -x["_score"])
        except Exception as ex:
            print(f"[ERROR] filter knn search: ", ex)
            knn_result = []
        return knn_result

    def get_boosting_score(self, elas_result):
        for r in elas_result:
            source = r["_source"]
            raw_score = r["_score"]
            boost_score = r.get("score", 1)
            r["_score"] = raw_score * boost_score
        return elas_result

    def mrr(self, rankedlists, weights, num_candidates):
        all_ids = set([])
        for rankedlist in rankedlists:
            all_ids.update(rankedlist)

        scores = {item:[] for item in all_ids}

        for k, rankedlist in enumerate(rankedlists):
            ids_ranks = {item:i for i,item in enumerate(rankedlist)}
            for id_ in all_ids:
                if id_ in ids_ranks:
                    scores[id_].append(weights[k] / (ids_ranks[id_] + 1))
                else:
                    scores[id_].append(weights[k] /  (num_candidates + 1))
        _scores = {k:np.sum(v) for k,v in scores.items()}
        
        return list(sorted(_scores.keys(), key = lambda x: -_scores[x])), _scores

    def get_all_item(self, customer, index_name="", filter_query={"match_all": {}}):  
        if not index_name.strip():
            index_name = self.faq_index
        result = []
        query = filter_query
        print(query)
        scroll_id = None
        scroll = "1d"
        # total = self.elas.count(index=index_name, body={"query": query})["count"]

        while True:
            try:
                if scroll_id is None:
                    self.elas.indices.refresh(index=index_name)
                    res_ = self.elas.search(index=index_name, body={"query": query}, size=10000, scroll=scroll)
                else:
                    res_ = self.elas.scroll(scroll_id=scroll_id, scroll=scroll)
                scroll_id = res_["_scroll_id"]
                print(scroll_id)
                dt= res_["hits"]["hits"]
                
                result.extend(dt)
                print(len(result))  
                if len(dt) == 0:
                    self.elas.clear_scroll(scroll_id=scroll_id)
                    break
            except Exception as ex:
                print(ex)
        return result

    def search_item_keyword(self, customer, index_name=""):
        query = {
            "bool": {
                "must": [
                    {"match_phrase": {"customer": customer}},
                    {"regexp": {"keyword": ".+"} }
                ]
            }
        }
        keyword_data = self.get_all_item(customer, index_name, filter_query=query)
        keyword_data = [x["_source"] for x in keyword_data]
        dict_rule = { x["keyword"]: {
            "keyword": x["keyword"],
            "answer": x["Text"]
        } for x in keyword_data if x.get("keyword","").strip() and x.get("Text","").strip()}
        rules = dict(sorted(list(dict_rule.items()), key = lambda x: -len(x[0])))
        return rules

class DocumentHandler():
    def get_document_id(self, query):
        hash_id = hashlib.sha224(query.encode("utf-8")).hexdigest()
        return hash_id
    
    def passage_blocks(self, txt, window_size, overlap):
        sens = nltk.sent_tokenize(txt)
        blocks = []
        for i in range(0, len(sens), window_size-overlap):
            b = sens[i:i+window_size]
            b = " ".join(b)
            blocks.append(b)
        print(blocks)
        return blocks


if __name__ == '__main__':
    client = FaqElasticsearch()
    client.search_item_keyword(customer="e7060b16-0637-41d3-9823-5d8ce829eb06", index_name="qna_chatbot_staging")