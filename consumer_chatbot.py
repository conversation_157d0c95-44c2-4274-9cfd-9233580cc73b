import pika
import json
import asyncio
import functools
import logging
import datetime
from typing import Dict, Any, Set
from rabbit_common import ICRabbitMQ, queue_chatbot_message_params_llm
from function.process_messages import RasaChatProcessor, DEFAULT_CONFIG
from function.save_result import save_result_chatbot

# Logger Setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

LOG_FILE_PATH = "chat_conversation_log.txt"

processed_messages: Set[str] = set()
last_message_content: Dict[str, str] = {}

def log_message_to_file(received_message: Dict[str, Any], processed_response: Dict[str, Any]):
    """Appends received and processed messages to a log file."""
    try:
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        with open(LOG_FILE_PATH, "a", encoding="utf-8") as f:
            f.write(f"[{timestamp}] Received:\n")
            json.dump(received_message, f, ensure_ascii=False, indent=2)
            f.write("\n")
            f.write(f"[{timestamp}] Processed Response:\n")
            json.dump(processed_response, f, ensure_ascii=False, indent=2)
            f.write("\n---\n")
    except Exception as e:
        logger.error(f"Error writing to log file {LOG_FILE_PATH}: {e}")

# Initialize Rasa Chat Processor
chat_processor = RasaChatProcessor(config=DEFAULT_CONFIG)

if not chat_processor.agent:
    logger.critical("Rasa agent failed to load. Exiting...")
    exit(1)

def sync(f):
    @functools.wraps(f)
    def wrapper(*args, **kwargs):
        try:
            loop = asyncio.get_event_loop()
            if loop.is_closed():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        return loop.run_until_complete(f(*args, **kwargs))
    return wrapper

@sync
async def callback_func(ch, method, properties, body):
    logger.info(f"Received message. Delivery Tag: {method.delivery_tag}")
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            body_data: Dict[str, Any] = json.loads(body.decode("utf-8"))

            if not chat_processor.agent:
                logger.error("Rasa agent not available. Skipping message. Nacking.")
                ch.basic_nack(delivery_tag=method.delivery_tag, requeue=True)
                return

            # Lấy thông tin user và message
            user_id = body_data.get('user_ID', body_data.get('conversation_id', 'default_user'))
            message_content = body_data.get('text', '')
            message_id = body_data.get('message_id')

            # Kiểm tra message trùng lặp
            is_duplicate = False
            
            # 1. Kiểm tra theo message_id nếu có
            if message_id and message_id in processed_messages:
                logger.info(f"Duplicate message_id detected: {message_id}")
                is_duplicate = True
            
            # 2. Kiểm tra theo nội dung message cho cùng user
            if message_content and user_id in last_message_content:
                if message_content.strip() == last_message_content[user_id].strip():
                    logger.info(f"Duplicate message content detected for user {user_id}")
                    is_duplicate = True

            if is_duplicate:
                logger.info(f"Skipping duplicate message. Delivery Tag: {method.delivery_tag}")
                ch.basic_ack(delivery_tag=method.delivery_tag)
                return

            # Xử lý message mới
            processed_result = await chat_processor.process_message(body_data, is_web_request=True)
            
            # Cập nhật lịch sử message đã xử lý
            if message_id:
                processed_messages.add(message_id)
            if message_content:
                last_message_content[user_id] = message_content
            
            log_message_to_file(body_data, processed_result)
            save_result_chatbot(processed_result)

            ch.basic_ack(delivery_tag=method.delivery_tag)
            logger.info(f"Processing and logging complete for tag {method.delivery_tag}")
            break
            
        except json.JSONDecodeError as json_err:
            logger.error(f"JSON decode error: {json_err}. Body: {body[:200]}. Nacking message.")
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
            break
            
        except Exception as e:
            retry_count += 1
            if retry_count >= max_retries:
                logger.error(f"Max retries ({max_retries}) exceeded for message {method.delivery_tag}. Error: {e}", exc_info=True)
                ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
            else:
                logger.warning(f"Retry {retry_count}/{max_retries} for message {method.delivery_tag}. Error: {e}")
                await asyncio.sleep(1 * retry_count)

if __name__ == '__main__':
    logger.info("Starting RabbitMQ Consumer...")

    if not chat_processor.agent:
        logger.critical("Rasa Agent not loaded. Exiting.")
        exit(1)

    usr_name = queue_chatbot_message_params_llm["UserName"]
    password = str(queue_chatbot_message_params_llm["Password"])
    host = queue_chatbot_message_params_llm["HostName"]
    virtual_host = queue_chatbot_message_params_llm["VirtualHost"]
    queue_name = queue_chatbot_message_params_llm["Queue"]
    prefetch_count = queue_chatbot_message_params_llm.get('PrefetchCount', 1)
    
    logger.info(f"Connecting to RabbitMQ: {host}/{virtual_host}")

    max_retry_attempts = 5
    retry_delay = 1
    retry_count = 0

    while True:
        try:
            credentials = pika.PlainCredentials(usr_name, password)
            connection_params = pika.ConnectionParameters(
                host=host, 
                virtual_host=virtual_host, 
                credentials=credentials, 
                heartbeat=600,
                blocked_connection_timeout=300,
                retry_delay=1,
                connection_attempts=3
            )
            connection = pika.BlockingConnection(connection_params)
            channel = connection.channel()
            
            channel.queue_declare(queue=queue_name, durable=True, arguments={"x-max-priority": 10})
            logger.info(f"Connected. Waiting for messages on '{queue_name}'")
            
            channel.basic_qos(prefetch_count=1)
            channel.basic_consume(queue=queue_name, on_message_callback=callback_func)
            
            retry_count = 0
            retry_delay = 1
            
            channel.start_consuming()
        except pika.exceptions.AMQPConnectionError as amqp_err:
            retry_count += 1
            if retry_count >= max_retry_attempts:
                logger.error(f"Max retry attempts ({max_retry_attempts}) exceeded. Last error: {amqp_err}")
                break
            
            logger.error(f"Connection Error (Attempt {retry_count}/{max_retry_attempts}): {amqp_err}. Retrying in {retry_delay} seconds...")
            asyncio.run(asyncio.sleep(retry_delay))
            retry_delay = min(retry_delay * 2, 60)
        except Exception as ex:
            logger.error(f"Unexpected error: {ex}", exc_info=True)
            asyncio.run(asyncio.sleep(10))