# deduplicate_texts.py
import time

start = time.time()

input_file = "output_deduplicated_min5.txt"
output_file = "output_deduplicated_new.txt"

# Đọc các dòng không rỗng
with open(input_file, "r", encoding="utf-8") as f:
    lines = [line.strip() for line in f if line.strip()]

# Loại bỏ trùng lặp
unique_lines = set(lines)

# Lọc các dòng:
# - có ít nhất 5 từ
# - KHÔNG chứa "atvncg" (không phân biệt hoa thường)
filtered_lines = [
    line for line in unique_lines
    if len(line.split()) >= 5 and "atvncg" not in line.lower()
]

# Ghi kết quả ra file
with open(output_file, "w", encoding="utf-8") as f:
    for line in sorted(filtered_lines):  # Bỏ sorted nếu không cần sắp xếp
        f.write(line + "\n")

end = time.time()
print(f"✅ Đã lọc {len(lines) - len(filtered_lines)} dòng trùng lặp, qu<PERSON>, hoặc chứa 'ATVNCG'")
print(f"📄 Còn lại {len(filtered_lines)} dòng hợp lệ, lưu vào '{output_file}'")
print(f"⏱️ Thời gian xử lý: {end - start:.2f} giây")
