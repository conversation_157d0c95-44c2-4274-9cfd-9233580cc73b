import json
import time
import datetime
from fastapi import FastAP<PERSON>, UploadFile, Form
from fastapi.middleware.cors import CORSMiddleware
from typing import List
from typing_extensions import Annotated

from database.elastic_search import FaqElasticsearch
from database.rabbit_common import push_to_queue, CONFIG
from database.customer import CustomerClient
from database.open_domain_config import OpenDomainConfigElasticsearch
from database.common_config import CommonConfigClient

from entity import *
from process_message import reply_web

from core import clear_conversation_history, conversation_db

FAQ_HOST = "http://**********:9200"
FAQ_INDEX = "qna_chatbot_staging"

faq_client = FaqElasticsearch(host=FAQ_HOST)
customer_client = CustomerClient()
open_domain_config_clent = OpenDomainConfigElasticsearch()
common_config_client = CommonConfigClient()

OPEN_DOMAIN_CONFIG_INDEX = open_domain_config_clent.config_index

tags_metadata = [
    # {
    #     "name": "users",
    #     "description": "Operations with users. The **login** logic is also here.",
    # },
    # {
    #     "name": "items",
    #     "description": "Manage items. So _fancy_ they have their own docs.",
    #     "externalDocs": {
    #         "description": "Items external docs",
    #         "url": "https://fastapi.tiangolo.com/",
    #     },
    # },
]

app = FastAPI(title="Chatbot GPT", openapi_tags=tags_metadata)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.post("/api/v1/common_cofig/list", tags=["common config"])
def get_config():
    status = 1
    message = "success"
    res = {}

    try:
        res = common_config_client.get_common_config()
    except Exception as ex:
        status = 1
        message = f"error: {str(ex)}"
        print("ERROR:", str(ex))
    result = {
        "status": status,
        "message": message,
        "result": res
    }
    return result

@app.post("/api/v1/common_cofig/update", tags=["common config"])
def update_common_config(item: UpdateCommonConfig):
    status = 1
    message = "success"
    res = []
    total = 0
    
    try:
        res = common_config_client.update_config(item.data)
    except Exception as ex:
        status = 1
        message = f"error: {str(ex)}"
        print("ERROR:", str(ex))
    result = {
        "status": status,
        "message": message,
        "result": res
    }
    return result

@app.post("/api/v1/customer/list", tags=["customer"])
def list_customer():
    status = 1
    message = "success"
    res = {}

    try:
        res = customer_client.get_customers()
    except Exception as ex:
        status = 1
        message = f"error: {str(ex)}"
        print("ERROR:", str(ex))
    result = {
        "status": status,
        "message": message,
        "result": res
    }
    return result

@app.post("/api/v1/customer/create_bulk", tags=["customer"])
def add_customer_bulk(items: List[Customer]):
    status = 1
    count = {
        "success": 0,
        "error": 0
    }
    for item in items:
        print(item)
        try:
            customer_client.add_customer(item.__dict__)
            count["success"] += 1
        except Exception as ex:
            print(ex)
            count["error"] += 1
    result = {
        "status": status,
        "result": count
    }
    return result

@app.post("/api/v1/customer/update_bulk", tags=["customer"])
def update_customer_bulk(items: List[UpdateCustomer]):
    status = 1
    message = "success"
    
    update_records = []
    for item in items:
        update_records.append(item)
        customer_client.update_customer(data_update=item.data, key=item.key)

    result = {
        "status": status,
        "message": message
    }
    return result

@app.delete("/api/v1/customer/delete_bulk", tags=["customer"])
def delete_customer_bulk(items: Ids):
    status = 1
    message = "success"

    try:
        customer_client.delete_customer(items.ids)
    except Exception as ex:
        status = 1
        message = f"error: {str(ex)}"
        print("ERROR:", str(ex))
    result = {
        "status": status,
        "message": message
    }
    return result

@app.post("/api/v1/open_domain/list", tags=["open_domain_config"])
def list_open_domain_service(item: ListOpenDomain):
    status = 1
    message = "success"
    res = []
    total = 0
    
    try:
        res, total = open_domain_config_clent.get_items(index_name=OPEN_DOMAIN_CONFIG_INDEX, page=item.page, size=item.size)
    except Exception as ex:
        status = 1
        message = f"error: {str(ex)}"
        print("ERROR:", str(ex))
    result = {
        "status": status,
        "message": message,
        "total": total,
        "result": res
    }
    return result

@app.post("/api/v1/open_domain/all", tags=["open_domain_config"])
def list_open_domain_service():
    status = 1
    message = "success"
    res = []
    total = 0
    
    try:
        res, total = open_domain_config_clent.get_items(index_name=OPEN_DOMAIN_CONFIG_INDEX, page=1, size=10000)
    except Exception as ex:
        status = 1
        message = f"error: {str(ex)}"
        print("ERROR:", str(ex))
    result = {
        "status": status,
        "message": message,
        "total": total,
        "result": res
    }
    return result


@app.post("/api/v1/open_domain/create_bulk", tags=["open_domain_config"])
def add_open_domain_bulk(items: List[OpenDomain]):
    status = 1
    message = "success"
    data = [item.__dict__ for item in items]
    try:
        open_domain_config_clent.add_items(index_name=OPEN_DOMAIN_CONFIG_INDEX, data=data)
    except Exception as ex:
        print(ex)
        message = f"error: {str(ex)}"
    result = {
        "status": status,
        "message": message,
    }
    return result

@app.post("/api/v1/open_domain/update_bulk", tags=["open_domain_config"])
def update_customer_bulk(items: List[UpdateOpenDomain]):
    status = 1
    message = "success"
    
    update_records = [x.__dict__ for x in items]
    open_domain_config_clent.update_items(update_records, index_name=OPEN_DOMAIN_CONFIG_INDEX)

    result = {
        "status": status,
        "message": message
    }
    return result

@app.delete("/api/v1/open_domain/delete_bulk", tags=["open_domain_config"])
def delete_customer_bulk(items: Ids):
    status = 1
    message = "success"

    try:
        open_domain_config_clent.delete_items(index_name=OPEN_DOMAIN_CONFIG_INDEX, ids=items.ids)
    except Exception as ex:
        status = 1
        message = f"error: {str(ex)}"
        print("ERROR:", str(ex))
    result = {
        "status": status,
        "message": message
    }
    return result

@app.post("/api/v1/faq/search_item" , tags=["FAQ"])
def search_item(item: SearchItemInput):
    status = 1
    message = "success"
    res = []
    try:
        res = faq_client.search_item(
            index_name=FAQ_INDEX, query=item.query, 
            customer=item.customer, top_k=item.top_k, 
            num_candidates=item.num_candidates, keywords=item.keywords, 
            sortby=item.sortby, use_only_query=item.use_only_query, 
            semantic_weight=item.semantic_weight
        )
    except Exception as ex:
        # raise ex
        status = 1
        message = f"error: {str(ex)}"
        print("ERROR:", str(ex))
    result = {
        "status": status,
        "message": message,
        "result": res
    }
    return result

@app.post("/api/v1/faq/list" , tags=["FAQ"])
def list_faq(item: ListFAQ):
    status = 1
    message = "success"
    res = []
    total = 0
    try:
        res, total = faq_client.get_items(item.customer, FAQ_INDEX, item.page, item.size, item.query)
    except Exception as ex:
        status = 1
        message = f"error: {str(ex)}"
        print("ERROR:", str(ex))
    result = {
        "status": status,
        "message": message,
        "total": total,
        "result": res
    }
    return result


@app.post("/api/v1/faq/create", tags=["FAQ"])
def add_faq(item: FAQ):
    print(item)
    message = 'success'
    status = 1
    try:
        faq_client.add_item(FAQ_INDEX, item.__dict__)
    except Exception as ex:
        status = -1
        message = f"error: {str(ex)}"

    return {
        "status": status,
        "message": message
    }
@app.post("/api/v1/faq/create_bulk", tags=["FAQ"])
def add_faq_bulk(items: List[FAQ]):
    status = 1
    count = {
        "success": 0,
        "error": 0
    }
    for item in items:

        try:
            faq_client.add_item(FAQ_INDEX, item.__dict__)
            count["success"] += 1
        except Exception as ex:
            count["error"] += 1
    result = {
        "status": status,
        "result": count
    }
    return result

@app.post("/api/v1/faq/update_bulk", tags=["FAQ"])
def update_faq_bulk(items: List[UpdateFAQ]):
    print(items)
    status = 1
    message = "success"
    
    update_records = []
    for item in items:
        update_records.append(item.__dict__)

    try:
        faq_client.update_items(update_records, FAQ_INDEX)
    except Exception as ex:
        status = 1
        message = f"error: {str(ex)}"
        print("ERROR:", str(ex))
    result = {
        "status": status,
        "message": message
    }
    return result

@app.post("/api/v1/faq/delete_bulk", tags=["FAQ"])
def delete_faq_bulk(items: Ids):
    status = 1
    message = "success"

    try:
        faq_client.delete_items(FAQ_INDEX, items.ids)
    except Exception as ex:
        status = 1
        message = f"error: {str(ex)}"
        print("ERROR:", str(ex))
    result = {
        "status": status,
        "message": message
    }
    return result

@app.post("/api/v1/faq/import_file")
def import_file(customer: Annotated[str, Form()], file: UploadFile):
    status = 1
    message = "success"
    try:
        save_path = f"data/{datetime.datetime.now()}_{file.filename}"
        with open(save_path, "wb") as f:
            f.write(file.file.read())

        data_push = {
            "filename": file.filename,
            "save_path": save_path,
            "customer": str(customer)
        }

        push_to_queue(data_push, CONFIG["import_file_faq_params"])
    except Exception as ex:
        status = -1
        message = f"error: {str(ex)}"
    return {
        
        "filename": file.filename,
        "status": status,
        "message": message
    }

@app.post("/chatbot")
async def chat_tcb_web(item: InputWeb):
    print(item)
    result  = None
    # try:
    #     item.retrieval_threshold = float(item.retrieval_threshold)
    # except Exception as ex:
    #     print(ex)
    #     item.retrieval_threshold = 0.5

    if not item.context: item.context=""
    try:
        input_data = {
            "text": item.text,
            "context": item.context,
            "user_ID": item.user_ID,
            "attachments": item.attachments,
            "customer": item.customer,
            "chatbot_open_domain": item.chatbot_open_domain,
            "created_time": time.time()
        }
        result  = await reply_web(input_data)
    except Exception as ex:
        raise ex
    # print(result)
    return result

@app.post("/chatbot_async")
async def chat_web_async(item: InputWebAsync):
    message = "successs"
    status = 1
    # try:
    #     item.retrieval_threshold = float(item.retrieval_threshold)
    # except Exception as ex:
    #     print(ex)
    #     item.retrieval_threshold = 0.5
    if not item.context: item.context=""
    if not item.time_range_str: item.time_range_str=""
    try:
        input_data = {
            "id": item.message_id,
            "text": item.text,
            "context": item.context,
            "user_ID": item.user_ID,
            "attachments": item.attachments,
            "customer": item.customer,
            "chatbot_open_domain": item.chatbot_open_domain,
            "created_time": time.time(),
            "start_time": item.start_time,
            "end_time": item.end_time,
            "time_range_str": item.time_range_str,
            "topic": item.topic
        }
        push_to_queue(input_data, CONFIG["chatbot_app_async_params"])
       
    except Exception as ex:
        message = str(ex)
        status = -1
    # print(result)
    return {
        "message": message,
        "status": status
    }

@app.post("/refresh")
def refresh_conversation(user_ID: str):

    status = 1
    message = "success"
    try:
        # clear_conversation_history(user_ID)
        conversation_db.delete_conversation(user_ID)
    except Exception as ex:
        status = -1
        message = f"error: {str(ex)}"
    return {
        "status": status,
        "message": message
    }


@app.post("/api/v1/chatbot_async")
async def chat_product_async(item: InputProduct):
    status = 1
    message = "success"

    data_push = item.__dict__
    data_push["created_time"] = time.time()
    try:
        with open(f"/home2/vietle/chatbot/chatbot-advance/requests/req_{time.time()}.json", "w", encoding="utf-8") as f:
            json.dump(data_push, f, ensure_ascii=False)
    except:
        pass
    try:
        with open('push_req.json', 'w') as f:
            json.dump(data_push, f , ensure_ascii=False)
        # with open(f'/home2/vietle/chatbot/chatbot-advance/requests/push_req_{time.time()}.json', 'w') as f:
        #     json.dump(data_push, f , ensure_ascii=False)
        # if data_push.get("page_id", "") == "285134531496649":
        #     push_to_queue(data_push, CONFIG["chatbot_message_params_rasa"])
        # else:
        #     push_to_queue(data_push, CONFIG["chatbot_message_params"])
        push_to_queue(data_push, CONFIG["chatbot_message_params_rasa"])
    except Exception as ex:
        message = f"error: {str(ex)}"
        status = -1
    result = {
        "message": message,
        "status": status
    }
    return result
