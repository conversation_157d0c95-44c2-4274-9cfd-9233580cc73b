import google.generativeai as genai
import os
import json
from google.generativeai.types import GenerationConfig # Cần import GenerationConfig
# Để định nghĩa schema, bạn cũng có thể cần:
import google.ai.generativelanguage as glm

# --- Cấu hình API Key ---
# Cách 1: Sử dụng biến môi trường (khuyến nghị)
# API_KEY = os.getenv("GEMINI_API_KEY")

# Cách 2: Gán trực tiếp (ít an toàn hơn, không nên commit vào git)
API_KEY = "AIzaSyDStExoFEob9iCveobRk4iUZN4-qbeXU8A" # THAY THẾ BẰNG API KEY CỦA BẠN

if not API_KEY:
    raise ValueError("Vui lòng đặt GEMINI_API_KEY trong biến môi trường hoặc gán trực tiếp.")

genai.configure(api_key=API_KEY)

# --- Khởi tạo Model ---
# Gemini 1.5 Flash hoặc Pro hỗ trợ tốt JSON mode
# Kiểm tra model 'gemini-2.0-flash' có tồn tại không, nếu không, dùng 'gemini-2.0-flash'
try:
    model_name = 'gemini-2.0-flash'
    model = genai.GenerativeModel(model_name)
    # Thử một lệnh gọi nhỏ để kiểm tra model
    # model.generate_content("test")
    print(f"Đã khởi tạo model: {model_name}")
except Exception as e:
    print(f"Lỗi khi khởi tạo model '{model_name}': {e}")
    print("Vui lòng kiểm tra lại tên model hoặc thử 'gemini-2.0-flash'.")
    exit()


def create_question_keywords_json(keywords, answer, quantity=20, language="vi"):
    """
    Tạo một chuỗi JSON chứa từ khóa gốc, từ khóa mới và danh sách các cặp (từ khóa liên quan, câu hỏi)
    sử dụng Gemini API với JSON mode.

    Args:
        keywords (list or str): Danh sách từ khóa hoặc một chuỗi từ khóa (phân tách bằng dấu phẩy).
        answer (str): Câu trả lời đầy đủ.
        quantity (int): Số lượng câu hỏi mong muốn (ước chừng).
        language (str): Mã ngôn ngữ (ví dụ: "vi" cho Tiếng Việt, "en" cho Tiếng Anh).

    Returns:
        str | None: Một chuỗi JSON hợp lệ, hoặc None nếu có lỗi.
              Cấu trúc mong muốn trong chuỗi JSON:
              {
                  "original_keywords": ["kw1_goc", "kw2_goc"],
                  "new_keywords": ["kw1_moi", "kw2_moi"],
                  "generated_items": [
                      {"associated_keywords": ["kw_lien_quan_q1"], "question": "Câu hỏi 1?"},
                      {"associated_keywords": ["kw_lien_quan_q2_a", "kw_lien_quan_q2_b"], "question": "Câu hỏi 2?"}
                  ]
              }
    """
    if isinstance(keywords, list):
        keyword_str = ", ".join(keywords)
    else:
        keyword_str = keywords

    prompt = f"""
    **Nhiệm vụ:**
    Dựa trên "Từ khóa gốc" và "Câu trả lời" được cung cấp bằng tiếng {language}, hãy tạo một đối tượng JSON.
    Đối tượng JSON này phải chứa các khóa sau:
    1.  `original_keywords`: Một danh sách (list of strings) các từ khóa gốc đã được cung cấp.
    2.  `new_keywords`: Một danh sách (list of strings) các từ khóa mới quan trọng được trích xuất từ "Câu trả lời". Các từ khóa này nên bổ sung ý nghĩa cho từ khóa gốc và liên quan trực tiếp đến nội dung câu trả lời. Nếu không có từ khóa mới nào đáng kể, trả về danh sách rỗng `[]`.
    3.  `generated_items`: Một danh sách (list of objects). Mỗi đối tượng trong danh sách này phải có hai khóa:
        *   `associated_keywords`: Một danh sách (list of strings) các từ khóa (có thể từ gốc hoặc mới, hoặc cả hai) liên quan trực tiếp nhất đến nội dung cụm từ sinh ra.
        *   `keyword_or_phrase`: Một chuỗi (string) chứa từ khóa hoặc cụm từ ngắn (không phải câu hoàn chỉnh). Số lượng item trong `generated_items` nên khoảng {quantity}.

    **Tiêu chí cho các cụm từ trong `generated_items`:**
    *   Chỉ bao gồm từ khóa hoặc cụm từ ngắn, không phải câu hỏi đầy đủ.
    *   Mỗi cụm từ phải tóm gọn một khía cạnh ý nghĩa quan trọng của "Câu trả lời".
    *   Từ khóa/cụm từ phải ngắn gọn, sát nghĩa, cô đọng và liên quan trực tiếp đến nội dung.
    *   Không lặp lại ý tưởng giữa các mục.
    *   Có thể là danh từ, danh từ ghép, cụm danh từ, hoặc cụm động từ ngắn nếu phù hợp.
    *   Đảm bỏa đủ số lượng items được yêu cầu.

    **QUAN TRỌNG - Định dạng đầu ra:**
    CHỈ trả về một chuỗi JSON hợp lệ theo cấu trúc đã mô tả ở trên.
    KHÔNG thêm bất kỳ văn bản nào trước hoặc sau chuỗi JSON (ví dụ: không dùng markdown code blocks như ```json ... ```).
    KHÔNG thêm bất kỳ giải thích nào.

    **Dữ liệu đầu vào để xử lý:**
    Ngôn ngữ: {language}
    Từ khóa gốc (keyword_str): "{keyword_str}"
    Câu trả lời (answer): "{answer}"
    Số lượng mục mong muốn (quantity): {quantity}

    **JSON OUTPUT:**
    """

    # Định nghĩa schema cho output JSON (giúp Gemini tuân thủ tốt hơn)
    # Đây là cách định nghĩa schema chi tiết, bạn có thể bỏ qua nếu prompt đủ mạnh
    # hoặc nếu model tự suy luận tốt.
    response_schema = glm.Schema(
        type=glm.Type.OBJECT,
        properties={
            'original_keywords': glm.Schema(type=glm.Type.ARRAY, items=glm.Schema(type=glm.Type.STRING)),
            'new_keywords': glm.Schema(type=glm.Type.ARRAY, items=glm.Schema(type=glm.Type.STRING)),
            'generated_items': glm.Schema(
                type=glm.Type.ARRAY,
                items=glm.Schema(
                    type=glm.Type.OBJECT,
                    properties={
                        'associated_keywords': glm.Schema(type=glm.Type.ARRAY, items=glm.Schema(type=glm.Type.STRING)),
                        'question': glm.Schema(type=glm.Type.STRING)
                    },
                    required=['associated_keywords', 'question']
                )
            )
        },
        required=['original_keywords', 'new_keywords', 'generated_items']
    )

    generation_config = GenerationConfig(
        response_mime_type="application/json",
        # response_schema=response_schema # Bật nếu bạn muốn ép schema chặt chẽ
        temperature=0.5 # Điều chỉnh nhiệt độ để kiểm soát sự sáng tạo
    )

    try:
        print(f"\n--- Gửi prompt đến Gemini cho keywords: '{keyword_str}' ---")
        # print("Prompt:\n", prompt) # Bỏ comment để debug prompt

        response = model.generate_content(
            prompt,
            generation_config=generation_config
        )

        if response.parts:
            generated_text = response.text
            print("--- Phản hồi JSON thô từ Gemini ---")
            print(generated_text)

            try:
                # Thực hiện một kiểm tra sơ bộ xem có phải JSON không bằng cách thử parse
                # nhưng sẽ trả về chuỗi gốc.
                json.loads(generated_text) 
                # Kiểm tra xem có vẻ là object JSON không (bắt đầu bằng { và kết thúc bằng })
                if generated_text.strip().startswith("{") and generated_text.strip().endswith("}"):
                    return generated_text # Trả về chuỗi JSON thô
                else:
                    print("Cảnh báo: Phản hồi từ Gemini không có dạng một đối tượng JSON hợp lệ (không bắt đầu bằng '{' hoặc kết thúc bằng '}').")
                    print("Phản hồi nhận được:", generated_text)
                    return None
            except json.JSONDecodeError as e:
                print(f"Lỗi: Phản hồi từ Gemini không phải là JSON hợp lệ. {e}")
                print("Phản hồi nhận được:", generated_text)
                return None
        else:
            print("Lỗi: Không có phần nào trong phản hồi từ Gemini.")
            if hasattr(response, 'prompt_feedback') and response.prompt_feedback:
                print(f"Phản hồi về prompt: {response.prompt_feedback}")
            if hasattr(response, 'candidates') and response.candidates:
                 for candidate in response.candidates:
                    if candidate.finish_reason != 1: # 1 = STOP
                        print(f"Ứng viên bị dừng vì: {candidate.finish_reason.name}")
                        if candidate.safety_ratings:
                            print(f"Safety ratings: {candidate.safety_ratings}")
            return None

    except Exception as e:
        print(f"Đã xảy ra lỗi khi gọi API Gemini: {e}")
        return None


if __name__ == "__main__":
    # Ví dụ 1:
    keywords_1 = ["Python", "học lập trình", "ngôn ngữ dễ học"]
    answer_1 = "Python là một ngôn ngữ lập trình bậc cao, thông dịch, dễ học và được sử dụng rộng rãi trong phát triển web, khoa học dữ liệu, trí tuệ nhân tạo và nhiều lĩnh vực khác. Cú pháp rõ ràng của nó giúp người mới bắt đầu dễ tiếp cận."

    print("\n--- Ví dụ 1 ---")
    result_1_json_str = create_question_keywords_json(keywords_1, answer_1, quantity=2)
    if result_1_json_str:
        print("\nChuỗi JSON trả về:")
        print(result_1_json_str)
        try:
            result_1_dict = json.loads(result_1_json_str)
            print("\nKết quả sau khi parse lại từ chuỗi JSON:")
            print(json.dumps(result_1_dict, indent=2, ensure_ascii=False)) # In đẹp JSON
            print("\nChi tiết các mục được tạo:")
            print(f"Từ khóa gốc: {result_1_dict.get('original_keywords')}")
            print(f"Từ khóa mới: {result_1_dict.get('new_keywords')}")
            for i, item in enumerate(result_1_dict.get('generated_items', []), 1):
                print(f"  Mục {i}:")
                print(f"    Từ khóa liên quan: {item.get('associated_keywords')}")
                print(f"    Câu hỏi: {item.get('question')}")
        except json.JSONDecodeError:
            print("Lỗi: Không thể parse chuỗi JSON trả về từ hàm.")

    # Ví dụ 2:
    keywords_2 = "biến đổi khí hậu, nguyên nhân, giải pháp"
    answer_2 = "Biến đổi khí hậu chủ yếu do phát thải khí nhà kính từ hoạt động của con người như đốt nhiên liệu hóa thạch và phá rừng. Các giải pháp bao gồm chuyển đổi sang năng lượng tái tạo, cải thiện hiệu quả sử dụng năng lượng và trồng rừng."

    print("\n--- Ví dụ 2 ---")
    result_2_json_str = create_question_keywords_json(keywords_2, answer_2, quantity=3, language="vi")
    if result_2_json_str:
        print("\nChuỗi JSON trả về:")
        print(result_2_json_str)
        try:
            result_2_dict = json.loads(result_2_json_str)
            print("\nKết quả sau khi parse lại từ chuỗi JSON:")
            print(json.dumps(result_2_dict, indent=2, ensure_ascii=False))
            print("\nChi tiết các mục được tạo:")
            print(f"Từ khóa gốc: {result_2_dict.get('original_keywords')}")
            print(f"Từ khóa mới: {result_2_dict.get('new_keywords')}")
            for i, item in enumerate(result_2_dict.get('generated_items', []), 1):
                print(f"  Mục {i}:")
                print(f"    Từ khóa liên quan: {item.get('associated_keywords')}")
                print(f"    Câu hỏi: {item.get('question')}")
        except json.JSONDecodeError:
            print("Lỗi: Không thể parse chuỗi JSON trả về từ hàm.")

    # Ví dụ 3: Không có từ khóa mới rõ ràng
    keywords_3 = ["thời tiết hôm nay"]
    answer_3 = "Hôm nay trời nắng đẹp, nhiệt độ khoảng 30 độ C."
    print("\n--- Ví dụ 3 ---")
    result_3_json_str = create_question_keywords_json(keywords_3, answer_3, quantity=1)
    if result_3_json_str:
        print("\nChuỗi JSON trả về:")
        print(result_3_json_str)
        try:
            result_3_dict = json.loads(result_3_json_str) # Parse lại để sử dụng
            print("\nKết quả sau khi parse lại từ chuỗi JSON:")
            print(json.dumps(result_3_dict, indent=2, ensure_ascii=False))
        except json.JSONDecodeError:
            print("Lỗi: Không thể parse chuỗi JSON trả về từ hàm.")