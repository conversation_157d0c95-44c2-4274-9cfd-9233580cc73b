import requests
import os
import base64
# import cv2
import urllib.parse


class SeaweedFS(object):
    def __init__(self, host, port):
        self.host = host
        self.port = port
        self.url = "http://" + host + ":" + str(port)

    def upload_file(self, file_name, folder):
        url_upload = urllib.parse.urljoin(self.url, folder) + "/"
        name_return = os.path.join(folder, file_name[file_name.rfind("/") + 1:])
        files = {'file': open(file_name, 'rb')}
        response = requests.post(url_upload, files=files)
        if response.status_code != 201:
            print(response.text)
            return ""
        return name_return

    def delete_file(self, file_name):
        url_delete = urllib.parse.urljoin(self.url, file_name)
        response = requests.delete(url_delete)
        print(response.text)
        return file_name

    def get_file(self, file_name):
        url_file = urllib.parse.urljoin(self.url, file_name)
        response = requests.get(url_file)
        b64 = None
        if response.status_code == 200:
            b64 = ("data:" + response.headers['Content-Type'] + ";" + "base64," +
                   base64.b64encode(response.content).decode("utf-8"))
        return b64

    # def upload_file_from_array(self, arr, file_path_upload):
    #     url_upload = urllib.parse.urljoin(self.url, file_path_upload)
    #     success, encoded_image = cv2.imencode('.jpg', arr)
    #     arr_bytes = encoded_image.tobytes()
    #     files = {'file': arr_bytes}
    #     response = requests.post(url_upload, files=files)
    #     if response.status_code != 201:
    #         print(response.text)
    #         return ""
    #     return file_path_upload

    def upload_file_from_bytes(self, byte_content, file_path_upload):
        url_upload = urllib.parse.urljoin(self.url, file_path_upload)
        files = {'file': byte_content}
        response = requests.post(url_upload, files=files)
        if response.status_code != 201:
            print(response.text)
            return ""
        return file_path_upload


if __name__ == "__main__":
    url_pattern = "https://graph.facebook.com/v3.2/$$$$1/picture"
    sf = SeaweedFS("10.9.2.225", 8888)
    url_sw = "http://10.9.2.225:8888/"
    url_sw = 'http://10.9.3.50:8888'
