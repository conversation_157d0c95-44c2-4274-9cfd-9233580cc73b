import pika
import json
import asyncio
import functools
from database.rabbit_common import ICRabbitMQ, CONFIG
# from process_message import reply_web_v3 as reply_web
from process_message import reply_web_remove_duplicate as reply_web
from function.save_result import save_result_chatbot_socialcare
import time

# def sync(f):
#     @functools.wraps(f)
#     def wrapper(*args, **kwargs):
#         return asyncio.get_event_loop().run_until_complete(f(*args, **kwargs))
#     return wrapper

# @sync
# async def callback_func(ch, method, properties, body):

import datetime
def get_current_str_time():
    return "_".join(str(datetime.datetime.now()).split())

def callback_func(ch, method, properties, body):
    print("receive done: ")
    status_code = None
    start = time.time()
    ch.basic_ack(delivery_tag=method.delivery_tag)
    body = json.loads(body.decode("utf-8"))
    res = reply_web(body, process_batch=True)
    print(res)
    
    # with open(f'/home2/vietle/chatbot/chatbot-advance/responses/res_{time.time()}.json', 'w', encoding="utf-8") as f:
    #     json.dump(res, f, ensure_ascii=False)
    
    time_processing = time.time() - start
    print(f"Time processed: {time_processing}")
    
    start2 = time.time()
    if res is not None and time_processing <= 30:
        status_code = save_result_chatbot_socialcare(res)
    time_save_result_chatbot = time.time() - start2
    print(f"Time save_result_chatbot: {time_save_result_chatbot}")
    time_processed_total = time.time() - start
    print(f"Time processed total: {time_processed_total}")
    
    
    try:
        # with open(f'/home2/vietle/chatbot/chatbot-advance/reqs/record_{time.time()}.json', '', encoding="utf-8") as f:
        #     json.dump(record, f, ensure_ascii=False)
        with open(f'/home2/vietle/chatbot/chatbot-advance/responses/results.txt', 'a+', encoding="utf-8") as f:
            f.write(f"[status_code: {status_code}] [{get_current_str_time()}] [time_processing: {time_processing}] [time_save_result_chatbot: {time_save_result_chatbot}] [time_processed_total: {time_processed_total}]" + str(res)+"\n")
    except Exception as ex:
        print(f"error save logs: {ex}")
        pass
    
    # ch.basic_ack(delivery_tag=method.delivery_tag)



if __name__ == '__main__':

    queue_chatbot_message_params = CONFIG['chatbot_message_params']
    usr_name = queue_chatbot_message_params["UserName"]
    password = str(queue_chatbot_message_params["Password"])
    host = queue_chatbot_message_params["HostName"]
    virtual_host = queue_chatbot_message_params["VirtualHost"]
    queue_name = queue_chatbot_message_params["Queue"]
    prefetchCount = queue_chatbot_message_params.get('PrefetchCount',1)
    
    
    while True:
        try:
            credentials = pika.PlainCredentials(usr_name, password)
            connection = pika.BlockingConnection(
                pika.ConnectionParameters(host=host, virtual_host=virtual_host, credentials=credentials, heartbeat=3600, blocked_connection_timeout=3600))
            channel = connection.channel()
            channel.queue_declare(queue=queue_name, durable=True, arguments={"x-max-priority": 10})
            print(" * wait message")
            channel.basic_qos(prefetch_count=prefetchCount)
            channel.basic_consume(queue=queue_name, on_message_callback=callback_func)
            channel.start_consuming()
        except Exception as ex:
            # if not connection.is_closed():
            #     connection.close()
            print(f'consumer_process_message_socialcare.py [ERROR] ', ex)
            # raise ex