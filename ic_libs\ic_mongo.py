from pymongo import Mongo<PERSON>lient
from bson.objectid import ObjectId
from pymongo import ASCENDING, DESCENDING


class MongoDatabaseIO(object):

    def __init__(self, host, port, database_name):
        self.host = host
        self.port = port
        self.client = MongoClient(self.host, self.port)
        self.db = self.client[database_name]

    def connect_db(self):
        self.client = MongoClient(self.host, self.port)
        return self.client

    def close_connection(self):
        self.client.close()

    def insert_one_doc(self, collection_name, data_insert):
        coll_obj = self.db[collection_name]
        inserted_id = coll_obj.insert_one(data_insert).inserted_id
        return inserted_id

    def find_one(self, collection_name, dict_query):
        coll_obj = self.db[collection_name]
        return coll_obj.find_one(dict_query)

    def find_one_from_objectId(self, collection_name, object_id):
        return self.find_one(collection_name, {"_id": ObjectId(object_id)})

    def find_all(self, collection_name, dict_query):
        coll_obj = self.db[collection_name]
        return coll_obj.find(dict_query)

    def count_documents(self, collection_name, dict_query):
        coll_obj = self.db[collection_name]
        return coll_obj.count_documents(dict_query)

    def insert_bulk_docs(self, collection_name, data_bulk):
        coll_obj = self.db[collection_name]
        inserted_ids = coll_obj.insert_many(data_bulk).inserted_ids
        return inserted_ids

    def create_index(self, collection_name, field_index, asc=True):
        coll_obj = self.db[collection_name]
        if asc:
            coll_obj.create_index([(field_index, ASCENDING)], unique=True)
        else:
            coll_obj.create_index([(field_index, DESCENDING)], unique=True)

    def update_value(self, collection_name, dic_filter, update):
        coll_obj = self.db[collection_name]
        coll_obj.update_many(dic_filter, update)

    def delete_document(self, collection_name, dic_filter):
        coll_obj = self.db[collection_name]
        coll_obj.delete_many(dic_filter)


if __name__ == "__main__":
    pass
    # host = "**********"
    # post = 27017
    # db_mon = MongoDatabaseIO(host, post, "avatar")
    # db_mon.update_value("fb_user", {"fbid": 15737202081759298}, {"$set": {"fbid": 15}})
    # exit(1)
    # num_of_element = 1000000
    # start_time = time.time()
    # for i in range(num_of_element):
    #     id_time = int(time.time() * 10000000)
    #     data_i = {"fbid": id_time, "name": "Trương Đức Nam {0}".format(id_time),
    #               "tags": ["mongodb", "python", "pymongo"], "date": datetime.datetime.utcnow()}
    #     db_mon.insert_one_doc("fb_user", data_i)
    # print("time insert one: ", time.time() - start_time)
    #
    # start_time = time.time()
    # data_bulk = []
    # for i in range(num_of_element):
    #     id_time = int(time.time() * 10000000)
    #     data_i = {"fbid": id_time, "name": "Trương Đức Nam {0}".format(id_time),
    #               "tags": ["mongodb", "python", "pymongo"], "date": datetime.datetime.utcnow()}
    #     data_bulk.append(data_i)
    # db_mon.insert_bulk_docs("fb_user", data_bulk)
    # print("time insert bulk: ", time.time() - start_time)
