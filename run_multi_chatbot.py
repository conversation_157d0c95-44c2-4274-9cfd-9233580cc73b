import subprocess
from multiprocessing import Process


def run_consumer():
    proc = subprocess.Popen("python consumer_process_message.py", shell=True)
    print(proc.pid)
    proc.communicate()

if __name__ == '__main__':
    execs = []
    
    n_pro = 10
    for i in range(n_pro):
        ex = Process(target=run_consumer, args=())
        execs.append(ex)
        ex.start()

    for exe in execs:
        exe.join()