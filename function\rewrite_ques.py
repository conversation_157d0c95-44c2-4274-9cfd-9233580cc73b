import requests
import json
import asyncio

from sqlalchemy.util import await_only


async def rewrite_question(question, context="", api_url="http://10.9.3.241:2032/api/llama3", **kwargs):
    headers = {
        'accept': 'application/json',
        'Content-Type': 'application/json'
    }

    prompt = (
        "Viết lại câu hỏi dưới đây thành một câu duy nhất, ngắn gọn, rõ ràng và đúng trọng tâm vấn đề cần hỏi, giữ nguyên ý nghĩa.\n"
        f"Câu hỏi gốc: {question}"
    )

    payload = {
        "questions": [prompt],
        "contexts": [context],
        "lang": "vi",
        "use_en_model": False,
        "batch_size": 1,
        "max_decoding_length": 256,
        "max_input_length": 4000,
        "repetition_penalty": 1,
        "temperature": 0.1,
        "do_sample": True,
        "no_repeat_ngram_size": 0,
        "is_translate_prompt": False,
        "is_translate_context": False,
        "is_translate_result": False,
        "version": "",
        "model_name": "",
        "system_prompt": "",
        "synthesize": "",
        "thinking_mode": "off",
        "add_generation_prompt": True,
        "tokenize": False,
        "log_dir": ""
    }

    payload.update(kwargs)

    try:
        response = requests.post(
            api_url,
            headers=headers,
            data=json.dumps(payload),
            timeout=30
        )
        response.raise_for_status()
        result = response.json()
        if 'result' in result and isinstance(result['result'], list) and len(result['result']) > 0:
            return result['result'][0]
        else:
            return result

    except requests.exceptions.RequestException as e:
        return {"error": f"Request failed: {str(e)}"}
    except json.JSONDecodeError as e:
        return {"error": f"JSON decode error: {str(e)}"}

if __name__ == "__main__":
    print("=== Ví dụ 1: Rewrite câu hỏi đơn giản ===")
    question = "Mình muốn mở thẻ"
    result = asyncio.run(rewrite_question(question))
    print(f"Câu hỏi gốc: {question}")
    print(result)