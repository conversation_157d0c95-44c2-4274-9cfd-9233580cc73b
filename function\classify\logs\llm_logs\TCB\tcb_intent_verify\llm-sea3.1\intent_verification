[2025-06-24 13:57:04,221]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-24 13:57:04,224]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-24 13:57:04,225]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.2255056: {"questions": ["Ki\u1ec3m tra xem c\u00e2u h\u1ecfi d\u01b0\u1edbi \u0111\u00e2y c\u00f3 thu\u1ed9c ch\u1ee7 \u0111\u1ec1 ch\u0103m s\u00f3c kh\u00e1ch h\u00e0ng c\u1ee7a ng\u00e2n h\u00e0ng hay kh\u00f4ng.\n\n- C\u00e2u h\u1ecfi: .\n- \u0110\u1ecbnh ngh\u0129a ch\u1ee7 \u0111\u1ec1: Ng\u01b0\u1eddi d\u00f9ng mu\u1ed1n t\u00ecm hi\u1ec3u v\u1ec1 th\u1ebb Techcombank Visa Eco, bao g\u1ed3m \u0111\u1eb7c \u0111i\u1ec3m, quy\u1ec1n l\u1ee3i, \u01b0u \u0111\u00e3i, \u0111i\u1ec1u ki\u1ec7n v\u00e0 th\u1ee7 t\u1ee5c m\u1edf th\u1ebb, bi\u1ec3u ph\u00ed, h\u1ea1n m\u1ee9c s\u1eed d\u1ee5ng, h\u01b0\u1edbng d\u1eabn \u0111\u0103ng k\u00fd, c\u0169ng nh\u01b0 c\u00e1c ch\u01b0\u01a1ng tr\u00ecnh khuy\u1ebfn m\u00e3i ho\u1eb7c h\u1ed7 tr\u1ee3 li\u00ean quan \u0111\u1ebfn lo\u1ea1i th\u1ebb n\u00e0y.\n\nN\u1ebfu c\u00e2u h\u1ecfi li\u00ean quan tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 \u0111\u1ec1, tr\u1ea3 v\u1ec1 1. N\u1ebfu kh\u00f4ng th\u00ec tr\u1ea3 v\u1ec1 0.  \nCh\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-24 13:57:04,739]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.2255056: ["1"]; time analysis [real - llm]: 0.**************** - 0.*****************
[2025-06-24 13:57:04,739]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.2255056: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-24 14:09:16,040]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-24 14:09:16,043]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-24 14:09:16,044]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm 1750748956.0439878: {"questions": ["Ki\u1ec3m tra xem c\u00e2u h\u1ecfi d\u01b0\u1edbi \u0111\u00e2y c\u00f3 thu\u1ed9c ch\u1ee7 \u0111\u1ec1 ch\u0103m s\u00f3c kh\u00e1ch h\u00e0ng c\u1ee7a ng\u00e2n h\u00e0ng hay kh\u00f4ng.\n\n- C\u00e2u h\u1ecfi: .\n- \u0110\u1ecbnh ngh\u0129a ch\u1ee7 \u0111\u1ec1: Ng\u01b0\u1eddi d\u00f9ng mu\u1ed1n bi\u1ebft v\u1ec1 kh\u1ea3 n\u0103ng, ph\u1ea1m vi h\u1ed7 tr\u1ee3, ho\u1eb7c c\u00e1c ch\u1ee9c n\u0103ng m\u00e0 bot/h\u1ec7 th\u1ed1ng c\u00f3 th\u1ec3 th\u1ef1c hi\u1ec7n.\n\nN\u1ebfu c\u00e2u h\u1ecfi li\u00ean quan tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 \u0111\u1ec1, tr\u1ea3 v\u1ec1 1. N\u1ebfu kh\u00f4ng th\u00ec tr\u1ea3 v\u1ec1 0.  \nCh\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-24 14:09:16,885]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm 1750748956.0439878: ["0"]; time analysis [real - llm]: 0.8414022922515869 - 0.07385516166687012
[2025-06-24 14:09:16,885]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm 1750748956.0439878: [{"answer": "0", "is_valid": true, "answer_norm": "0"}]
[2025-06-24 16:29:17,966]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-24 16:29:17,966]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-24 16:33:50,913]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-24 16:33:50,913]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-24 16:33:50,914]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm 1750757630.9146955: {"questions": ["Ki\u1ec3m tra xem c\u00e2u h\u1ecfi d\u01b0\u1edbi \u0111\u00e2y c\u00f3 thu\u1ed9c ch\u1ee7 \u0111\u1ec1 ch\u0103m s\u00f3c kh\u00e1ch h\u00e0ng c\u1ee7a ng\u00e2n h\u00e0ng hay kh\u00f4ng.\n\n- C\u00e2u h\u1ecfi: C\u00f3 ng\u01b0\u1eddi chuy\u1ec3n ti\u1ec1n nh\u1ea7m th\u00ec ph\u1ea3i l\u00e0m sao \u1ea1\n- \u0110\u1ecbnh ngh\u0129a ch\u1ee7 \u0111\u1ec1: Ng\u01b0\u1eddi d\u00f9ng g\u1eb7p s\u1ef1 c\u1ed1 chuy\u1ec3n ti\u1ec1n nh\u1ea7m, v\u00ed d\u1ee5 chuy\u1ec3n sai s\u1ed1 t\u00e0i kho\u1ea3n, chuy\u1ec3n nh\u1ea7m ng\u01b0\u1eddi nh\u1eadn, ho\u1eb7c nh\u1eadp sai th\u00f4ng tin khi giao d\u1ecbch. Ng\u01b0\u1eddi d\u00f9ng mu\u1ed1n \u0111\u01b0\u1ee3c h\u1ed7 tr\u1ee3 tra so\u00e1t, thu h\u1ed3i giao d\u1ecbch, ho\u1eb7c h\u1ecfi v\u1ec1 quy tr\u00ecnh gi\u1ea3i quy\u1ebft khi chuy\u1ec3n kho\u1ea3n nh\u1ea7m t\u1ea1i ng\u00e2n h\u00e0ng.\n\nN\u1ebfu c\u00e2u h\u1ecfi li\u00ean quan tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 \u0111\u1ec1, tr\u1ea3 v\u1ec1 1. N\u1ebfu kh\u00f4ng th\u00ec tr\u1ea3 v\u1ec1 0.  \nCh\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-24 16:33:51,265]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm 1750757630.9146955: ["1"]; time analysis [real - llm]: 0.3509178161621094 - 0.27846574783325195
[2025-06-24 16:33:51,266]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm 1750757630.9146955: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-24 16:34:08,809]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-24 16:34:08,810]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-24 16:34:08,810]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm 1750757648.810096: {"questions": ["Ki\u1ec3m tra xem c\u00e2u h\u1ecfi d\u01b0\u1edbi \u0111\u00e2y c\u00f3 thu\u1ed9c ch\u1ee7 \u0111\u1ec1 ch\u0103m s\u00f3c kh\u00e1ch h\u00e0ng c\u1ee7a ng\u00e2n h\u00e0ng hay kh\u00f4ng.\n\n- C\u00e2u h\u1ecfi: Em mu\u1ed1n h\u1ecfi l\u00e0 khi em chuy\u1ec3n kho\u1ea3n nh\u1ea7m cho ng\u01b0\u1eddi kh\u00e1c e c\u00f3 l\u1ea5y l\u1ea1i \u0111c ko \u1ea1 e \u0111ang d\u00f9ng ng\u00e2n h\u00e0ng tech \u1ea1\n- \u0110\u1ecbnh ngh\u0129a ch\u1ee7 \u0111\u1ec1: Ng\u01b0\u1eddi d\u00f9ng g\u1eb7p s\u1ef1 c\u1ed1 chuy\u1ec3n ti\u1ec1n nh\u1ea7m, v\u00ed d\u1ee5 chuy\u1ec3n sai s\u1ed1 t\u00e0i kho\u1ea3n, chuy\u1ec3n nh\u1ea7m ng\u01b0\u1eddi nh\u1eadn, ho\u1eb7c nh\u1eadp sai th\u00f4ng tin khi giao d\u1ecbch. Ng\u01b0\u1eddi d\u00f9ng mu\u1ed1n \u0111\u01b0\u1ee3c h\u1ed7 tr\u1ee3 tra so\u00e1t, thu h\u1ed3i giao d\u1ecbch, ho\u1eb7c h\u1ecfi v\u1ec1 quy tr\u00ecnh gi\u1ea3i quy\u1ebft khi chuy\u1ec3n kho\u1ea3n nh\u1ea7m t\u1ea1i ng\u00e2n h\u00e0ng.\n\nN\u1ebfu c\u00e2u h\u1ecfi li\u00ean quan tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 \u0111\u1ec1, tr\u1ea3 v\u1ec1 1. N\u1ebfu kh\u00f4ng th\u00ec tr\u1ea3 v\u1ec1 0.  \nCh\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-24 16:34:08,971]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm 1750757648.810096: ["1"]; time analysis [real - llm]: 0.16138505935668945 - 0.10597705841064453
[2025-06-24 16:34:08,972]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm 1750757648.810096: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-24 17:35:51,062]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-24 17:35:51,063]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-24 17:35:51,065]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm 1750761351.0651534: {"questions": ["Ki\u1ec3m tra xem c\u00e2u h\u1ecfi d\u01b0\u1edbi \u0111\u00e2y c\u00f3 thu\u1ed9c ch\u1ee7 \u0111\u1ec1 ch\u0103m s\u00f3c kh\u00e1ch h\u00e0ng c\u1ee7a ng\u00e2n h\u00e0ng hay kh\u00f4ng.\n\n- C\u00e2u h\u1ecfi: E xin s\u1ed1 hotline nh\u00e9\n- \u0110\u1ecbnh ngh\u0129a ch\u1ee7 \u0111\u1ec1: Ng\u01b0\u1eddi d\u00f9ng h\u1ecfi v\u1ec1 s\u1ed1 hotline, s\u1ed1 \u0111i\u1ec7n tho\u1ea1i t\u1ed5ng \u0111\u00e0i ho\u1eb7c k\u00eanh li\u00ean h\u1ec7 ch\u00ednh th\u1ee9c c\u1ee7a ng\u00e2n h\u00e0ng \u0111\u1ec3 \u0111\u01b0\u1ee3c t\u01b0 v\u1ea5n, h\u1ed7 tr\u1ee3 ho\u1eb7c gi\u1ea3i \u0111\u00e1p th\u1eafc m\u1eafc.\n\nN\u1ebfu c\u00e2u h\u1ecfi li\u00ean quan tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 \u0111\u1ec1, tr\u1ea3 v\u1ec1 1. N\u1ebfu kh\u00f4ng th\u00ec tr\u1ea3 v\u1ec1 0.  \nCh\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-24 17:36:12,093]-[ERROR]-[generate]-[(176)] [meta_log]:  - llm 1750761351.0651534: HTTPConnectionPool(host='**********', port=2033): Max retries exceeded with url: /api/llama3 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPConnection object at 0x0000017879717B80>, 'Connection to ********** timed out. (connect timeout=None)')); time analysis: 21.028550386428833
[2025-06-24 17:36:12,094]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm 1750761351.0651534: []
[2025-06-24 17:38:04,603]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-24 17:38:04,604]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-24 17:38:04,604]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm 1750761484.6048367: {"questions": ["Ki\u1ec3m tra xem c\u00e2u h\u1ecfi d\u01b0\u1edbi \u0111\u00e2y c\u00f3 thu\u1ed9c ch\u1ee7 \u0111\u1ec1 ch\u0103m s\u00f3c kh\u00e1ch h\u00e0ng c\u1ee7a ng\u00e2n h\u00e0ng hay kh\u00f4ng.\n\n- C\u00e2u h\u1ecfi: E xin s\u1ed1 hotline nh\u00e9\n- \u0110\u1ecbnh ngh\u0129a ch\u1ee7 \u0111\u1ec1: Ng\u01b0\u1eddi d\u00f9ng h\u1ecfi v\u1ec1 s\u1ed1 hotline, s\u1ed1 \u0111i\u1ec7n tho\u1ea1i t\u1ed5ng \u0111\u00e0i ho\u1eb7c k\u00eanh li\u00ean h\u1ec7 ch\u00ednh th\u1ee9c c\u1ee7a ng\u00e2n h\u00e0ng \u0111\u1ec3 \u0111\u01b0\u1ee3c t\u01b0 v\u1ea5n, h\u1ed7 tr\u1ee3 ho\u1eb7c gi\u1ea3i \u0111\u00e1p th\u1eafc m\u1eafc.\n\nN\u1ebfu c\u00e2u h\u1ecfi li\u00ean quan tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 \u0111\u1ec1, tr\u1ea3 v\u1ec1 1. N\u1ebfu kh\u00f4ng th\u00ec tr\u1ea3 v\u1ec1 0.  \nCh\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-24 17:38:04,797]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm 1750761484.6048367: ["1"]; time analysis [real - llm]: 0.19292807579040527 - 0.09298300743103027
[2025-06-24 17:38:04,798]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm 1750761484.6048367: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-24 17:38:50,249]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-24 17:38:50,249]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-24 17:38:50,251]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm 1750761530.2510598: {"questions": ["Ki\u1ec3m tra xem c\u00e2u h\u1ecfi d\u01b0\u1edbi \u0111\u00e2y c\u00f3 thu\u1ed9c ch\u1ee7 \u0111\u1ec1 ch\u0103m s\u00f3c kh\u00e1ch h\u00e0ng c\u1ee7a ng\u00e2n h\u00e0ng hay kh\u00f4ng.\n\n- C\u00e2u h\u1ecfi: Cho ch\u1ecb xin sdt hotline dc ko\n- \u0110\u1ecbnh ngh\u0129a ch\u1ee7 \u0111\u1ec1: Ng\u01b0\u1eddi d\u00f9ng h\u1ecfi v\u1ec1 s\u1ed1 hotline, s\u1ed1 \u0111i\u1ec7n tho\u1ea1i t\u1ed5ng \u0111\u00e0i ho\u1eb7c k\u00eanh li\u00ean h\u1ec7 ch\u00ednh th\u1ee9c c\u1ee7a ng\u00e2n h\u00e0ng \u0111\u1ec3 \u0111\u01b0\u1ee3c t\u01b0 v\u1ea5n, h\u1ed7 tr\u1ee3 ho\u1eb7c gi\u1ea3i \u0111\u00e1p th\u1eafc m\u1eafc.\n\nN\u1ebfu c\u00e2u h\u1ecfi li\u00ean quan tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 \u0111\u1ec1, tr\u1ea3 v\u1ec1 1. N\u1ebfu kh\u00f4ng th\u00ec tr\u1ea3 v\u1ec1 0.  \nCh\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-24 17:39:01,693]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm 1750761530.2510598: ["1"]; time analysis [real - llm]: 11.44212532043457 - 0.09678483009338379
[2025-06-24 17:39:01,694]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm 1750761530.2510598: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
[2025-06-25 16:24:04,663]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 16:24:04,664]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 16:24:04,666]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm **********.664975: {"questions": ["Ki\u1ec3m tra xem c\u00e2u h\u1ecfi d\u01b0\u1edbi \u0111\u00e2y c\u00f3 thu\u1ed9c ch\u1ee7 \u0111\u1ec1 ch\u0103m s\u00f3c kh\u00e1ch h\u00e0ng c\u1ee7a ng\u00e2n h\u00e0ng hay kh\u00f4ng.\n\n- C\u00e2u h\u1ecfi: Xin t\u01b0 v\u1ea5n\n- \u0110\u1ecbnh ngh\u0129a ch\u1ee7 \u0111\u1ec1: Ng\u01b0\u1eddi d\u00f9ng mu\u1ed1n t\u00ecm hi\u1ec3u v\u1ec1 th\u1ebb Techcombank Visa Eco, bao g\u1ed3m \u0111\u1eb7c \u0111i\u1ec3m, quy\u1ec1n l\u1ee3i, \u01b0u \u0111\u00e3i, \u0111i\u1ec1u ki\u1ec7n v\u00e0 th\u1ee7 t\u1ee5c m\u1edf th\u1ebb, bi\u1ec3u ph\u00ed, h\u1ea1n m\u1ee9c s\u1eed d\u1ee5ng, h\u01b0\u1edbng d\u1eabn \u0111\u0103ng k\u00fd, c\u0169ng nh\u01b0 c\u00e1c ch\u01b0\u01a1ng tr\u00ecnh khuy\u1ebfn m\u00e3i ho\u1eb7c h\u1ed7 tr\u1ee3 li\u00ean quan \u0111\u1ebfn lo\u1ea1i th\u1ebb n\u00e0y.\n\nN\u1ebfu c\u00e2u h\u1ecfi li\u00ean quan tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 \u0111\u1ec1, tr\u1ea3 v\u1ec1 1. N\u1ebfu kh\u00f4ng th\u00ec tr\u1ea3 v\u1ec1 0.  \nCh\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 16:24:07,519]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm **********.664975: ["0"]; time analysis [real - llm]: 2.**************** - 0.*****************
[2025-06-25 16:24:07,519]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm **********.664975: [{"answer": "0", "is_valid": true, "answer_norm": "0"}]
[2025-06-25 16:27:22,103]-[WARNING]-[__init__]-[(102)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove.txt
[2025-06-25 16:27:22,103]-[WARNING]-[__init__]-[(111)] File not found: C:\Users\<USER>\PycharmProjects\chatbot-rasa\function\classify\logs\llm_logs\post_process\llm\remove_prefix.txt
[2025-06-25 16:27:22,104]-[INFO]-[generate]-[(165)] [meta_log]:  - input llm 1750843642.104098: {"questions": ["Ki\u1ec3m tra xem c\u00e2u h\u1ecfi d\u01b0\u1edbi \u0111\u00e2y c\u00f3 thu\u1ed9c ch\u1ee7 \u0111\u1ec1 ch\u0103m s\u00f3c kh\u00e1ch h\u00e0ng c\u1ee7a ng\u00e2n h\u00e0ng hay kh\u00f4ng.\n\n- C\u00e2u h\u1ecfi: ph\u1ea3i l\u00e0m g\u00ec \u0111\u1ec3 kh\u1eafc ph\u1ee5c \u1ea1?\n- \u0110\u1ecbnh ngh\u0129a ch\u1ee7 \u0111\u1ec1: Ng\u01b0\u1eddi d\u00f9ng mu\u1ed1n kh\u00f3a th\u1ebb (ATM, t\u00edn d\u1ee5ng, ghi n\u1ee3) c\u1ee7a m\u00ecnh, th\u01b0\u1eddng do b\u1ecb m\u1ea5t, th\u1ea5t l\u1ea1c, nghi ng\u1edd gian l\u1eadn, ho\u1eb7c th\u1ebb b\u1ecb nu\u1ed1t v\u00e0o m\u00e1y ATM.\n\nN\u1ebfu c\u00e2u h\u1ecfi li\u00ean quan tr\u1ef1c ti\u1ebfp \u0111\u1ebfn ch\u1ee7 \u0111\u1ec1, tr\u1ea3 v\u1ec1 1. N\u1ebfu kh\u00f4ng th\u00ec tr\u1ea3 v\u1ec1 0.  \nCh\u1ec9 tr\u1ea3 v\u1ec1 0 ho\u1eb7c 1, kh\u00f4ng gi\u1ea3i th\u00edch."], "contexts": [""], "lang": "vi", "max_decoding_length": 64, "system_prompt": "", "max_input_length": 4000, "is_translate_prompt": true, "is_translate_context": true, "is_translate_result": true, "repetition_penalty": 1.1, "temperature": 0.1}
[2025-06-25 16:27:22,293]-[INFO]-[generate]-[(172)] [meta_log]:  - output llm 1750843642.104098: ["1"]; time analysis [real - llm]: 0.18906283378601074 - 0.09933257102966309
[2025-06-25 16:27:22,293]-[INFO]-[generate]-[(194)] [meta_log]:  - output norm llm 1750843642.104098: [{"answer": "1", "is_valid": true, "answer_norm": "1"}]
