import os
import json
import asyncio
import logging
import time
from copy import deepcopy
from typing import Dict, Any, Optional, List, Callable
import re
from cachetools import TTLCache
from dataclasses import dataclass, field
from datetime import datetime

from .classify.question_classifier import ClassificationQuestion, classify_response
from .classify.double_check_by_llm import IntentVerification

# --- Inputs (Global configurations and paths) ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

DEFAULT_FALLBACK_MESSAGE = "Cảm ơn bạn đã cung cấp thông tin. Sẽ có chuyên viên tư vấn phản hồi bạn sớm ạ."

# --- Logger Setup ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

@dataclass
class PendingMessage:
    """Represents a message waiting to be processed"""
    record: Dict[str, Any]
    server_timestamp: float  # Khi server nhận được
    client_timestamp: Optional[float]  # Từ created_time nếu có
    is_web_request: bool
    
    @property
    def effective_timestamp(self) -> float:
        """Sử dụng client timestamp nếu có, ngược lại dùng server timestamp"""
        return self.client_timestamp if self.client_timestamp else self.server_timestamp
    
@dataclass
class UserBatch:
    """Represents a batch of messages for a specific user"""
    user_id: str
    messages: List[PendingMessage] = field(default_factory=list)
    timer_task: Optional[asyncio.Task] = None
    first_message_client_time: Optional[float] = None
    first_message_server_time: float = 0.0
    
    def add_message(self, message: PendingMessage):
        """Add a message to the batch"""
        if not self.messages:
            self.first_message_server_time = message.server_timestamp
            self.first_message_client_time = message.client_timestamp
        self.messages.append(message)
    
    def get_combined_text(self) -> str:
        """Combine all message texts"""
        texts = []
        for msg in self.messages:
            text = msg.record.get("text", "").strip()
            if text:
                texts.append(text)
        return " , ".join(texts)  # Separate multiple messages with ,

    def get_latest_record(self) -> Dict[str, Any]:
        """Get the most recent message record as base for response"""
        return self.messages[-1].record if self.messages else {}
    
    def get_time_span_seconds(self) -> float:
        """Calculate time span using client timestamps if available"""
        if not self.messages:
            return 0.0
            
        # Sử dụng client timestamps nếu có
        if self.first_message_client_time and self.messages[-1].client_timestamp:
            return self.messages[-1].client_timestamp - self.first_message_client_time
        
        # Fallback về server timestamps
        return self.messages[-1].server_timestamp - self.first_message_server_time

DEFAULT_CONFIG: Dict[str, Any] = {
    "classifier_app_code": "TCB",
    "classifier_function_code": "tcb_classify",
    "classifier_model_llm": "llm-sea3.1",
    "classifier_url_llm_api": "http://**********:2033/api/llama3",
    "classifier_is_get_prompt_online": False,
    "debug_write_record_json": True,
    "record_json_path": os.path.join(SCRIPT_DIR, "debug_record.json"),
    "intent_verification_app_code": "TCB",
    "intent_verification_function_code": "tcb_intent_verify",
    "intent_verification_model_llm": "llm-sea3.1",
    "intent_verification_url_llm_api": "http://**********:2033/api/llama3",
    "intent_verification_is_get_prompt_online": False,
    "batching_enabled": True,
    "batch_timeout_seconds": 25.0,
    "max_batch_size": 5,
    "batch_duplicate_check_enabled": True,
    "batch_history_ttl_seconds": 600,
    "max_message_gap_seconds": 30.0,
}

def convert_buttons_from_payload(buttons_payload: List[Dict[str, Any]], platform: str) -> List[Dict[str, Any]]:
    logger.info(f"Placeholder: convert_buttons_from_payload called with platform: {platform}, buttons_payload: {buttons_payload}")
    return buttons_payload

async def rewrite_question(question: str) -> str:
    return question

class LLMChatProcessor:
    def __init__(self, config: Optional[Dict[str, Any]] = None, 
                 response_callback: Optional[Callable[[str, Dict[str, Any]], None]] = None):
        self.config = deepcopy(DEFAULT_CONFIG)
        if config:
            self.config.update(config)
        
        # Response callback to send batched responses
        self.response_callback = response_callback
        
        # Batching system
        self.batching_enabled = self.config.get("batching_enabled", True)
        self.batch_timeout = self.config.get("batch_timeout_seconds", 10.0)
        self.max_batch_size = self.config.get("max_batch_size", 5)
        self.max_message_gap = self.config.get("max_message_gap_seconds", 30.0)
        history_ttl = self.config.get("batch_history_ttl_seconds", 600)
        self.user_batches: Dict[str, UserBatch] = {}
        self.batch_lock = asyncio.Lock()
        
        try:
            self.question_classifier = ClassificationQuestion(
                app_code=self.config.get("classifier_app_code", "TCB"),
                function_code=self.config.get("classifier_function_code", "tcb_classify"),
                model_llm=self.config.get("classifier_model_llm", "llm-sea3.1"),
                url_llm_api=self.config.get("classifier_url_llm_api", "http://**********:2033/api/llama3"),
                is_get_prompt_online=self.config.get("classifier_is_get_prompt_online", False)
            )
            logger.info("ClassificationQuestion engine initialized successfully for LLMChatProcessor.")
        except Exception as e:
            logger.critical(f"Failed to initialize ClassificationQuestion engine: {e}", exc_info=True)
            raise RuntimeError("Could not initialize Question Classifier") from e

        try:
            self.intent_verifier = IntentVerification(
                app_code=self.config.get("intent_verification_app_code", "TCB"),
                function_code=self.config.get("intent_verification_function_code", "tcb_intent_verify"),
                model_llm=self.config.get("intent_verification_model_llm", "llm-sea3.1"),
                url_llm_api=self.config.get("intent_verification_url_llm_api", "http://**********:2033/api/llama3"),
                is_get_prompt_online=self.config.get("intent_verification_is_get_prompt_online", False)
            )
            logger.info("IntentVerification engine initialized successfully for LLMChatProcessor.")
        except Exception as e:
            logger.critical(f"Failed to initialize IntentVerification engine: {e}", exc_info=True)
            raise RuntimeError("Could not initialize Intent Verifier") from e

        self.user_message_history = TTLCache(maxsize=10000, ttl=history_ttl)
        self.user_last_message_time = TTLCache(maxsize=10000, ttl=history_ttl)
        self.message_cooldown = 5.0

    def _clean_text(self, text: str) -> str:
        return text.strip()

    def _parse_created_time(self, record: Dict[str, Any]) -> Optional[float]:
        """Parse created_time từ record thành timestamp"""
        created_time = record.get("created_time")
        if not created_time:
            return None
            
        try:
            if isinstance(created_time, (int, float)):
                timestamp = float(created_time)
                if timestamp > 1000000000000:
                    return timestamp / 1000.0
                return timestamp
            
            # Nếu là string ISO format
            if isinstance(created_time, str):
                # Xử lý các format phổ biến
                formats = [
                    "%Y-%m-%dT%H:%M:%S.%fZ",     # 2024-01-15T10:05:03.123Z
                    "%Y-%m-%dT%H:%M:%SZ",        # 2024-01-15T10:05:03Z
                    "%Y-%m-%d %H:%M:%S.%f",      # 2024-01-15 10:05:03.123
                    "%Y-%m-%d %H:%M:%S",         # 2024-01-15 10:05:03
                    "%Y-%m-%dT%H:%M:%S.%f",      # 2024-01-15T10:05:03.123
                    "%Y-%m-%dT%H:%M:%S",         # 2024-01-15T10:05:03
                ]
                
                for fmt in formats:
                    try:
                        dt = datetime.strptime(created_time, fmt)
                        return dt.timestamp()
                    except ValueError:
                        continue
                        
                # Thử parse milliseconds timestamp string
                try:
                    ms_timestamp = int(created_time)
                    if ms_timestamp > 1000000000000:
                        return ms_timestamp / 1000.0
                    return float(ms_timestamp)
                except ValueError:
                    pass
                    
        except Exception as e:
            logger.warning(f"Could not parse created_time '{created_time}': {e}")
            
        return None

    async def _process_batch_timeout(self, user_id: str):
        """Handle batch timeout - process all collected messages for user"""
        await asyncio.sleep(self.batch_timeout)
        
        batch_to_process = None
        async with self.batch_lock:
            batch_to_process = self.user_batches.pop(user_id, None)
        
        if batch_to_process and batch_to_process.messages:
            logger.info(f"Processing batch for user '{user_id}' due to timeout ({self.batch_timeout}s) with {len(batch_to_process.messages)} messages.")
            await self._process_user_batch(batch_to_process)

    async def _process_user_batch(self, batch: UserBatch):
        """Process a complete batch of messages for a user"""
        if not batch.messages:
            return
            
        logger.info(f"Processing batch for user '{batch.user_id}' with {len(batch.messages)} messages")
        
        # Get combined text from all messages
        combined_text = batch.get_combined_text()
        latest_record = batch.get_latest_record()
        
        # Create a new record for processing with combined text and enhanced timing info
        batch_record = deepcopy(latest_record)
        batch_record["text"] = combined_text
        batch_record["batch_info"] = {
            "message_count": len(batch.messages),
            "time_span_seconds": batch.get_time_span_seconds(),
            "first_message_time": batch.first_message_client_time or batch.first_message_server_time,
            "last_message_time": batch.messages[-1].effective_timestamp,
            "using_client_timestamps": any(msg.client_timestamp for msg in batch.messages),
            "original_messages": [
                {
                    "text": msg.record.get("text", ""),
                    "client_time": msg.client_timestamp,
                    "server_time": msg.server_timestamp,
                    "created_time_raw": msg.record.get("created_time"),
                    "effective_time": msg.effective_timestamp
                }
                for msg in batch.messages
            ]
        }
        
        # Process the batched message
        is_web_request = batch.messages[-1].is_web_request
        response = await self._process_single_message(batch_record, is_web_request)
        
        logger.info(f"Batch processing complete for user '{batch.user_id}', sending response...")
        
        if self.response_callback:
            try:
                await asyncio.create_task(
                    self.response_callback(batch.user_id, response) 
                    if asyncio.iscoroutinefunction(self.response_callback)
                    else asyncio.to_thread(self.response_callback, batch.user_id, response)
                )
                logger.info(f"✅ Successfully sent batch response for user '{batch.user_id}'")
            except Exception as e:
                logger.error(f"❌ Error sending batch response via callback for user '{batch.user_id}': {e}")
        else:
            logger.error(f"❌ NO RESPONSE CALLBACK SET! Batch response cannot be sent for user '{batch.user_id}'")
            logger.error(f"Missing callback means user will not receive response: {response.get('actions', '')}")
            
            raise RuntimeError(f"Response callback not configured for batch processing. User '{batch.user_id}' will not receive response.")

    def _should_start_new_batch(self, current_batch: UserBatch, new_message: PendingMessage) -> bool:
        """Determine if we should start a new batch based on time gap"""
        if not current_batch.messages:
            return False
            
        last_message = current_batch.messages[-1]
        
        last_time = last_message.effective_timestamp
        new_time = new_message.effective_timestamp
        
        gap = new_time - last_time
        
        if gap > self.max_message_gap:
            logger.info(f"Message gap too large ({gap:.1f}s > {self.max_message_gap}s), starting new batch")
            return True
            
        return False

    async def _add_message_to_batch(self, record: Dict[str, Any], is_web_request: bool) -> bool:
        """Add message to user's batch with improved timestamp handling. Returns True if batched, False if should process immediately"""
        if not self.batching_enabled:
            return False
            
        user_id = record.get("user_ID", record.get("conversation_id", "default_user"))
        message_text = self._clean_text(record.get("text", ""))
        
        if not message_text:
            return False
            
        server_time = time.time()
        client_time = self._parse_created_time(record)
        
        if client_time:
            logger.debug(f"Using client timestamp for user '{user_id}': {client_time} (created_time: {record.get('created_time')})")
        else:
            logger.debug(f"Using server timestamp for user '{user_id}': {server_time}")
            
        pending_message = PendingMessage(
            record=record,
            server_timestamp=server_time,
            client_timestamp=client_time,
            is_web_request=is_web_request
        )
        
        async with self.batch_lock:
            if user_id not in self.user_batches:
                batch = UserBatch(user_id)
                batch.add_message(pending_message)
                self.user_batches[user_id] = batch
                
                batch.timer_task = asyncio.create_task(self._process_batch_timeout(user_id))
                logger.info(f"Started new batch for user '{user_id}' with timeout {self.batch_timeout}s")
                return True
            else:
                batch = self.user_batches[user_id]
                
                if batch.timer_task:
                    batch.timer_task.cancel()
                    logger.debug(f"Cancelled existing timer for user '{user_id}'.")

                if self._should_start_new_batch(batch, pending_message):
                    old_batch = self.user_batches.pop(user_id, None)
                    if old_batch:
                        await self._process_user_batch(old_batch)
                    
                    # Start new batch
                    new_batch = UserBatch(user_id)
                    new_batch.add_message(pending_message)
                    self.user_batches[user_id] = new_batch
                    new_batch.timer_task = asyncio.create_task(self._process_batch_timeout(user_id))
                    logger.info(f"Started new batch for user '{user_id}' due to time gap. New batch size: 1")
                    return True
                
                # Check for duplicates in batch if enabled
                if self.config.get("batch_duplicate_check_enabled", True):
                    normalized_text = message_text.lower()
                    for existing_msg in batch.messages:
                        existing_text = existing_msg.record.get("text", "").lower()
                        if normalized_text == existing_text:
                            logger.info(f"Duplicate message detected in batch for user '{user_id}': '{message_text}'")
                            return True  # Skip duplicate but keep in batching mode
                
                batch.add_message(pending_message)
                logger.info(f"Added message to existing batch for user '{user_id}'. New size: {len(batch.messages)}")
                
                # Check if batch is full
                if len(batch.messages) >= self.max_batch_size:
                    logger.info(f"Batch size limit reached for user '{user_id}', processing immediately.")
                    full_batch = self.user_batches.pop(user_id) # Pop the batch to process
                    await self._process_user_batch(full_batch)
                else:
                    # Reset the timer since a new message was added
                    batch.timer_task = asyncio.create_task(self._process_batch_timeout(user_id))
                    logger.debug(f"Reset batch timeout for user '{user_id}'.")

            return True

    async def process_message(self, record: Dict[str, Any], is_web_request: bool = False) -> Dict[str, Any]:
        """Main entry point for message processing with batching support and client timestamp handling"""
        
        try:
            # Try to add to batch first
            if await self._add_message_to_batch(record, is_web_request):
                # Message was batched - use silent batching strategy
                user_id = record.get("user_ID", record.get("conversation_id", "default_user"))
                logger.info(f"Message silently batched for user '{user_id}', will process with other messages")
                
                # Return silent response structure (đảm bảo JSON serializable)
                silent_response = {
                    "user_ID": user_id,
                    "user_name": record.get("user_name", ""),
                    "platform": record.get("platform", ""),
                    "actions": "",  # Empty action = không gửi message
                    "buttons": [],
                    "message": "batched_silent",
                    "error_code": 0,
                    "type": "silent",  # Special type for silent responses
                    "is_batched": True,
                    "should_send": False,  # Flag để client biết không cần gửi
                    "timestamp": time.time()
                }
                
                if not is_web_request:
                    return {"status": 1, "message": "batched_silent", "action": silent_response}
                return silent_response
            
            # Process immediately if not batched
            return await self._process_single_message(record, is_web_request)
            
        except Exception as e:
            logger.error(f"Error in process_message: {e}", exc_info=True)
            # Return error response instead of raising exception
            error_response = deepcopy(record)
            error_response["actions"] = DEFAULT_FALLBACK_MESSAGE
            error_response["buttons"] = []
            error_response["message"] = "error"
            error_response["error_code"] = 500
            error_response["type"] = "text"
            error_response["error_details"] = str(e)
            
            if not is_web_request:
                return {"status": 0, "message": "error", "action": error_response}
            return error_response

    async def _process_single_message(self, record: Dict[str, Any], is_web_request: bool = False) -> Dict[str, Any]:
        """Process a single message (original logic with timing improvements)"""
        message_text: str = self._clean_text(record.get("text", ""))
        
        if not message_text:
            logger.info("Empty message_text after cleaning.")
            return {"status": 0, "message": "empty_message", "action": {}}

        user_id: str = record.get("user_ID", record.get("conversation_id", "default_user"))
        current_time = time.time()

        # --- Duplicate Message Check (for non-batched mode) ---
        if not record.get("batch_info"):  # Only check duplicates for non-batched messages
            normalized_text = message_text.lower()
            if user_id not in self.user_message_history:
                self.user_message_history[user_id] = set()
                self.user_last_message_time[user_id] = 0.0

            if normalized_text in self.user_message_history[user_id]:
                # Sử dụng client timestamp nếu có để tính cooldown chính xác hơn
                client_timestamp = self._parse_created_time(record)
                last_message_time = self.user_last_message_time.get(user_id, 0)
                
                if client_timestamp:
                    time_since_last_message = client_timestamp - last_message_time
                else:
                    time_since_last_message = current_time - last_message_time
                
                if time_since_last_message < self.message_cooldown:
                    logger.info(f"Message cooldown active for user '{user_id}': '{message_text}' (gap: {time_since_last_message:.1f}s)")
                    cooldown_response = deepcopy(record)
                    cooldown_response["actions"] = ""
                    cooldown_response["buttons"] = []
                    cooldown_response["message"] = "cooldown"
                    cooldown_response["error_code"] = 2
                    cooldown_response["type"] = "text"
                    if not is_web_request:
                        return {"status": 0, "message": "cooldown", "action": cooldown_response}
                    return cooldown_response
            
            # Update last message time với client timestamp nếu có
            client_timestamp = self._parse_created_time(record)
            self.user_last_message_time[user_id] = client_timestamp if client_timestamp else current_time
            self.user_message_history[user_id].add(normalized_text)

        logger.info(f"Processing message for user '{user_id}': '{message_text}'")

        if self.config.get("debug_write_record_json", False):
            try:
                debug_record = deepcopy(record)
                if record.get("batch_info"):
                    debug_record["batch_processing"] = True
                with open(self.config["record_json_path"], 'w', encoding='utf-8') as f_json:
                    json.dump(debug_record, f_json, ensure_ascii=False, indent=2)
            except Exception as e_json:
                logger.error(f"Error writing debug record.json: {e_json}")
        
        try:
            message_text_rewritten = await rewrite_question(question=message_text)
        except Exception as e:
            logger.warning(f"Error rewriting question: {e}")
            message_text_rewritten = message_text

        final_response = deepcopy(record)
        user_name = record.get("user_name", "[user]")

        if is_web_request and (record.get("attachments") == 1 or (isinstance(record.get("attachments"), list) and len(record.get("attachments", [])) > 0)):
            logger.info(f"Message for user '{user_id}' has attachments.")
            final_response["actions"] = "Cảm ơn bạn đã cung cấp hình ảnh/tài liệu. Chuyên viên sẽ sớm liên hệ."
            final_response["buttons"] = []
            final_response["message"] = "success_attachment_fallback"
            final_response["error_code"] = 0
            final_response["type"] = "text"
            return final_response

        # --- Kiểm tra các ký tự vô nghĩa ---
        meaningless_patterns = [
            r"^\s*[.]+\s*$",
            r"^\s*[/]+\s*$",
            r"^\s*[?]+\s*$",
            r"^\s*$",
        ]
        if any(re.match(pat, message_text) for pat in meaningless_patterns):
            logger.info(f"Detected meaningless/gibberish input for user '{user_id}': '{message_text}' -> fallback response.")
            final_response = deepcopy(record)
            final_response["actions"] = DEFAULT_FALLBACK_MESSAGE
            final_response["buttons"] = []
            final_response["message"] = "nlu_fallback"
            final_response["error_code"] = 0
            final_response["type"] = "text"
            final_response['intent'] = {'name': 'nlu_fallback', 'confidence': 0.0}
            final_response['entities'] = []
            final_response['intent_ranking'] = [{'name': 'nlu_fallback', 'confidence': 0.0}]
            final_response['nlu_metadata'] = {}
            if not is_web_request:
                return {"status": 1, "message": "nlu_fallback", "action": final_response}
            return final_response

        # --- Phân loại câu hỏi bằng LLM ---
        nlu_result: Dict[str, Any]
        try:
            nlu_result = await asyncio.to_thread(
                self.question_classifier.classify_question_with_llm, 
                message_text_rewritten
            )
            logger.debug(f"NLU classification result: {nlu_result}")
        except Exception as e:
            logger.error(f"Error during NLU classification: {e}", exc_info=True)
            nlu_result = self.question_classifier._create_fallback_response(message_text_rewritten, time.time())

        # Ensure nlu_metadata exists
        if 'nlu_metadata' not in nlu_result:
            nlu_result['nlu_metadata'] = {}

        intent_name = nlu_result.get("intent", {}).get("name", "nlu_fallback")

        # --- Intent Verification ---
        is_verified_and_related = False
        if intent_name != "nlu_fallback":
            try:
                verification_response = self.intent_verifier.verify_intent_relevance(message_text_rewritten, intent_name)
                is_verified_and_related = verification_response.get("is_directly_related", False)
                logger.info(f"Intent '{intent_name}' verification result: {is_verified_and_related}")
                nlu_result['nlu_metadata']['double_check_status'] = verification_response.get('status', 'unknown_error')
            except Exception as e:
                logger.error(f"Error during intent verification: {e}", exc_info=True)
                is_verified_and_related = False
                nlu_result['nlu_metadata']['double_check_status'] = f"verification_error: {str(e)}"
        else:
            nlu_result['nlu_metadata']['double_check_status'] = "nlu_fallback"
            
        response_objects: List[Dict[str, Any]]
        if is_verified_and_related:
            response_objects = classify_response(intent_name)
        else:
            response_objects = [{
                "text": "Cảm ơn bạn đã cung cấp thông tin. Sẽ có chuyên viên tư vấn phản hồi bạn sớm ạ."
            }]
            logger.info(f"Using fallback message due to intent verification failure or nlu_fallback for intent: {intent_name}")
            nlu_result['intent'] = {'name': 'intent_double_check_fallback', 'confidence': 1.0}
            nlu_result['intent_ranking'] = [{'name': 'intent_double_check_fallback', 'confidence': 1.0}]

        actions_text_parts = []
        buttons_payload_parts = []

        if not response_objects:
            logger.warning(f"classify_response returned empty for intent '{intent_name}'. Using default fallback.")
            actions_text_parts.append(DEFAULT_FALLBACK_MESSAGE)
        else:
            for res_obj in response_objects:
                if "text" in res_obj:
                    actions_text_parts.append(res_obj["text"])
                if "buttons" in res_obj:
                    buttons_payload_parts.extend(res_obj["buttons"])
        
        final_actions_text = "\n".join(filter(None, actions_text_parts)).strip()
        if not final_actions_text:
             final_actions_text = DEFAULT_FALLBACK_MESSAGE
             logger.warning(f"No text found in response_objects for intent '{intent_name}', using default fallback.")

        final_response["actions"] = final_actions_text.replace("[user_name]", user_name)
        final_response["buttons"] = convert_buttons_from_payload(buttons_payload_parts, platform=record.get("platform", ""))
        final_response["message"] = "success"
        final_response["error_code"] = 0
        final_response["type"] = "buttons" if final_response["buttons"] else "text"
        
        final_response['intent'] = nlu_result.get('intent')
        final_response['entities'] = nlu_result.get('entities')
        final_response['intent_ranking'] = nlu_result.get('intent_ranking')
        final_response['nlu_metadata'] = nlu_result.get('metadata')

        # Add timing information to response if it's a batched message
        if record.get("batch_info"):
            final_response["batch_info"] = record["batch_info"]
            logger.info(f"Processed batched message for user '{user_id}' with {record['batch_info']['message_count']} messages")

        if not is_web_request:
            return {"status": 1, "message": "success", "action": final_response}

        logger.debug(f"Final response for user '{user_id}': {json.dumps(final_response, ensure_ascii=False, indent=2)}")
        return final_response

    async def force_process_pending_batches(self):
        """Force process all pending batches (useful for shutdown)"""
        async with self.batch_lock:
            user_ids = list(self.user_batches.keys())
            for user_id in user_ids:
                batch = self.user_batches.pop(user_id, None)
                if not batch:
                    continue
                if batch.timer_task:
                    batch.timer_task.cancel()
                if batch.messages:
                    await self._process_user_batch(batch)

    def set_response_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """Set callback function for sending batched responses
        
        IMPORTANT: This callback MUST be set for batch responses to be delivered to users.
        The callback should handle sending the response to the appropriate messaging platform.
        
        Args:
            callback: Function that takes (user_id: str, response: Dict[str, Any]) and sends the response
        """
        self.response_callback = callback
        logger.info("Response callback has been set for batch processing")

# CRITICAL: Example of proper callback implementation
async def proper_response_callback(user_id: str, response: Dict[str, Any]):
    """
    PROPER callback implementation that actually sends response to user
    This is what you should implement in your main application
    """
    try:
        actions = response.get("actions", "")
        buttons = response.get("buttons", [])
        platform = response.get("platform", "")
        
        if not actions.strip():
            logger.warning(f"Empty actions for user {user_id}, skipping send")
            return
        
        # **THIS IS WHERE YOU SEND TO YOUR ACTUAL PLATFORM**
        # Replace this with your actual platform sending logic:
        
        # For Zalo:
        # await zalo_api.send_message(user_id, actions, buttons)
        
        # For Facebook:
        # await facebook_api.send_message(user_id, actions, buttons)
        
        # For Web API:
        # await web_api.send_response(user_id, response)
        
        # For now, just log what SHOULD be sent:
        logger.info(f"🚀 SENDING TO PLATFORM ({platform}) for user {user_id}:")
        logger.info(f"   Actions: {actions}")
        logger.info(f"   Buttons: {len(buttons)} buttons")
        
        # **REMOVE THIS PRINT AND ADD YOUR ACTUAL SENDING CODE**
        print(f"📤 [BATCH RESPONSE] User: {user_id}")
        print(f"📝 Message: {actions}")
        if buttons:
            print(f"🔘 Buttons: {[btn.get('title', '') for btn in buttons]}")
        print("-" * 50)
        
    except Exception as e:
        logger.error(f"Failed to send batch response to user {user_id}: {e}")
        raise  # Re-raise to ensure error is visible