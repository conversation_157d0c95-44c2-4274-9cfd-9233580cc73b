recipe: default.v1

language: vi

pipeline:
- name: WhitespaceTokenizer
- name: <PERSON><PERSON><PERSON>eaturizer
- name: LexicalSyntacticFeaturizer
- name: CRFEntityExtractor
  # - name: CountVectorsFeaturizer
- name: CountVectorsFeaturizer
  analyzer: char_wb
  min_ngram: 1
  max_ngram: 4
#- name: LanguageModelFeaturizer
#    # Name of the language model to use
#  model_name: "sentence_transformers"
#    # Pre-Trained weights to be loaded
#  model_weights: "sentence-transformers/paraphrase-MiniLM-L6-v2"
- name: LanguageModelFeaturizer
  model_name: "bert"
  model_weights: "sentence-transformers/paraphrase-MiniLM-L6-v2"
    # model_weights: "rasa/LaBSE"

    # An optional path to a directory from which
    # to load pre-trained model weights.
    # If the requested model is not found in the
    # directory, it will be downloaded and
    # cached in this directory for future use.
    # The default value of `cache_dir` can be
    # set using the environment variable
    # `TRANSFORMERS_CACHE`, as per the
    # Transformers library.
#  cache_dir: /hdd/thienthan/chatbot-rasa/featurizer/models
#  cache_dir: /home1/data/thienthan/chatbot-rasa/featurizer/models
#- name: LanguageModelFeaturizer
#  model_name: "bert"
#  model_weights: "vinai/phobert-base"
#  cache_dir: /hdd/thienthan/chatbot-rasa/featurizer/models

- name: DIETClassifier
  epochs: 100
  constrain_similarities: true
  entity_recognition: true
- name: EntitySynonymMapper
- name: ResponseSelector
  epochs: 100
  constrain_similarities: true
- name: FallbackClassifier
  threshold: 0.3
  ambiguity_threshold: 0.05

# Configuration for Rasa Core.
# https://rasa.com/docs/rasa/core/policies/
policies: null
# # No configuration for policies was provided. The following default policies were used to train your model.
# # If you'd like to customize them, uncomment and adjust the policies.
# # See https://rasa.com/docs/rasa/policies for more information.
#   - name: MemoizationPolicy
#   - name: RulePolicy
#   - name: UnexpecTEDIntentPolicy
#     max_history: 5
#     epochs: 100
#   - name: TEDPolicy
#     max_history: 5
#     epochs: 100
#     constrain_similarities: true
assistant_id: 20250507-083532-cool-animation
#   - name: MemoizationPolicy
#   - name: RulePolicy
#   - name: UnexpecTEDIntentPolicy
#     max_history: 5
#     epochs: 100
#   - name: TEDPolicy
#     max_history: 5
#     epochs: 100
#     constrain_similarities: true
