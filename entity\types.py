from pydantic import BaseModel
from typing import Union, List


class Input(BaseModel):
    message: str
    user_ID: str = "abc123"


class InputWeb(BaseModel):
    text: str
    context: Union[str, None] = ""
    user_ID: str
    attachments: int = 0
    customer: str = 'vnvc'
    chatbot_open_domain: list = []


class InputWebAsync(BaseModel):
    start_time: Union[str, None] = ""  # str = "2023-06-30T09:25:27.727Z",
    end_time: Union[str, None] = ""  # str = "2023-06-30T09:25:27.727Z",
    time_range_str: Union[str, None] = ""
    text: str
    context: Union[str, None] = ""
    user_ID: str
    attachments: int = 0
    customer: str = 'e706010f-2a38-4fb8-8ee3-ca09f5230473'  # general
    chatbot_open_domain: list = []
    message_id: str
    topic: Union[str, None] = ""


class InputProduct(BaseModel):
    text: str
    conversation_id: str
    page_id: str
    attachments: list = []
    history: list = []
    platform: str
    fb_scope_id: str
    user_name: str = "user_name"
    is_first_message: bool = True
    is_duplicate_message: bool = False


class FAQ(BaseModel):
    Query: str
    Text: str
    customer: str
    score: float = 1.0
    keyword: str = ""


class UpdateFAQ(FAQ):
    id: str


class Ids(BaseModel):
    ids: list = []


class ListFAQ(BaseModel):
    page: int = 1
    size: int = 10
    query: str = ""
    customer: str = "e7060b16-0637-41d3-9823-5d8ce829eb06"


class ImportFile(BaseModel):
    # file_type: str = "xlsx"
    customer: str = 'e7060b16-0637-41d3-9823-5d8ce829eb06'


class Customer(BaseModel):
    key: str
    text: str
    description: str


class UpdateCustomer(BaseModel):
    key: str
    data: dict = {}


class OpenDomain(BaseModel):
    key: str
    name: str
    alias: str
    url: str
    format_input: str = ""
    format_output: str = ""


class ListOpenDomain(BaseModel):
    page: int = 1
    size: int = 10
    query: str = ""


class UpdateOpenDomain(BaseModel):
    id: str
    data: dict = {
        "key": "string",
        "name": "string",
        "alias": "string",
        "url": "string",
        "format_input": "",
        "format_output": ""
    }


class UpdateCommonConfig(BaseModel):
    data: dict = {
        "retrieval_threshold": 0.5,
        "ambiguous_threshold": 0.5,
        "sbert_weight": 0.5,
        "chatbot_open_domain": {
            "key": "chatgpt",
            "name": "ICGPT",
            "alias": "icgpt",
            "url": "https://chat.openai.com/",
            "format_input": "",
            "format_output": "",
        }
    }


class SearchItemInput(BaseModel):
    query: str
    top_k: int = 5
    num_candidates: int = 100
    keywords: List[str] = []
    sortby: str = "mrr"
    customer: str = "e705110a-3223-48de-b52c-12f85c30b6f8"
    use_only_query: bool = True
    semantic_weight: float = 0.5
