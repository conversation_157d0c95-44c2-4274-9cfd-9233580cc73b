
import requests
class ICRequest(object):
    def __init__(self, main_url, lst_backup_url = []):
        self.main_url = main_url
        self.lst_backup_url = lst_backup_url
    
    def request(self, method, **kwargs):
        result = requests.request(method, url=self.main_url, **kwargs)
        if result.status_code != 200:
            for url in self.lst_backup_url:
                result = requests.request(method, url=url, **kwargs)
                if result.status_code == 200:
                    # print(f"url backup {url} success")
                    return result
        else:
            pass
            # print(f"url {self.main_url} success")
        return result


