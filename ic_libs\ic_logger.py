
import logging
import time
import datetime
from logging.handlers import RotatingFileHandler

class ICLogger(object):
    def __init__(self, logDir='log', logFile='app.log', maxBytes = 1 * 1024 * 1024, backupCount = 2, prefix_log=''):

        self.logger = logging.getLogger(__name__)
        file = f'{logDir}/{logFile}'
        self.handler = RotatingFileHandler(file, mode='a', maxBytes=maxBytes, 
                                        backupCount=backupCount, encoding='utf-8', delay=False)
        self.logger.setLevel(logging.INFO)
        self.logger.addHandler(self.handler)

        if not prefix_log:
            prefix_log = logFile.split('.log')[0]
        self.prefix_log = f'[{prefix_log.upper()}] '


    def format(self):
        return f'[{datetime.datetime.now()}]'
    def info(self, text):
        self.logger.info(self.format() + '[INFO]' + self.prefix_log + str(text))
    
    def debug(self, text):
        self.logger.debug(self.format() + '[DEBUG]' + self.prefix_log + str(text))

    def warning(self, text):
        self.logger.warning(self.format() +'[WARNING]' +self.prefix_log + str(text))
    
    def critical(self, text):
        self.logger.critical(self.format() +'[CRITICAL]' +self.prefix_log + str(text))

    def error(self, text):
        self.logger.error(self.format() +'[ERROR]' +self.prefix_log + str(text))

