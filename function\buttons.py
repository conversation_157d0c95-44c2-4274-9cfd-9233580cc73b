
{
    "attachment": {
      "type": "template",
      "payload": {
        "template_type": "list",
        "elements": [
          {
            "title": "Official Account API",
            "subtitle": "There Is No Limit To What You Can Accomplish Using Zalo",
            "image_url": "https://stc-developers.zdn.vn/images/bg_1.jpg",
            "default_action": {
              "type": "oa.open.url",
              "url": "https://developers.zalo.me/docs/api/official-account-api-147"
            }
          },
          {
            "title": "Article API",
            "image_url": "https://stc-zaloprofile.zdn.vn/pc/v1/images/zalo_sharelogo.png",
            "default_action": {
              "type": "oa.open.url",
              "url": "https://developers.zalo.me/docs/api/article-api-151"
            }
          },

        ]
      }
    }
}

def convert_buttons(buttons, platform):
    if platform == 'zalo':
        texts = [x["title"] for x in buttons]
        return button_zalo(texts)
    else:
        return buttons
    
def button_zalo(texts, template_type="list"):
    elements = []
    for text in texts:
        ele = {
            "title": text,
            "subtitle": f"Xem thông tin về {text}",
            "image_url": "",
            "default_action": {
                "type": "oa.query.hide",
                "payload": text
            }
        }
        elements.append(ele)
    payload = {
        "template_type": template_type,
        "elements": elements
    }
    output = {
        "type": "template",
        "payload": payload
    }
    return output